import { useTheme } from '@/contexts/ThemeContext';
import { databaseService } from '@/services/database';
import { useAppStore } from '@/store/appStore';
import { validatePrice, validateQuantity } from '@/utils/dataProcessing';
import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Alert, KeyboardAvoidingView, Platform, ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function EditHoldingScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { updatePortfolioHolding, deletePortfolioHolding } = useAppStore();
  const { theme } = useTheme();

  const [holding, setHolding] = useState<any>(null);
  const [quantity, setQuantity] = useState('');
  const [averagePrice, setAveragePrice] = useState('');
  const [purchaseDate, setPurchaseDate] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    loadHolding();
  }, [id]);

  const loadHolding = async () => {
    try {
      const holdingData = await databaseService.getHolding(parseInt(id));
      if (holdingData) {
        setHolding(holdingData);
        setQuantity(holdingData.quantity.toString());
        setAveragePrice(holdingData.averagePrice.toString());
        setPurchaseDate(holdingData.purchaseDate);
      } else {
        Alert.alert('Error', 'Holding not found', [
          { text: 'OK', onPress: () => router.back() },
        ]);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to load holding data');
    }
  };

  const handleSave = async () => {
    // Validation
    const quantityNum = parseFloat(quantity);
    if (!quantity || isNaN(quantityNum) || !validateQuantity(quantityNum)) {
      Alert.alert('Error', 'Please enter a valid quantity (1-1,000,000)');
      return;
    }

    const priceNum = parseFloat(averagePrice);
    if (!averagePrice || isNaN(priceNum) || !validatePrice(priceNum)) {
      Alert.alert('Error', 'Please enter a valid price (₹0.01-₹10,00,000)');
      return;
    }

    if (!purchaseDate) {
      Alert.alert('Error', 'Please enter a purchase date');
      return;
    }

    setIsLoading(true);
    try {
      await updatePortfolioHolding(parseInt(id), {
        quantity: quantityNum,
        averagePrice: priceNum,
        purchaseDate,
      });

      Alert.alert('Success', 'Holding updated successfully', [
        { text: 'OK', onPress: () => router.back() },
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to update holding. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete Holding',
      `Are you sure you want to delete this ${holding?.ticker} holding? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: confirmDelete,
        },
      ]
    );
  };

  const confirmDelete = async () => {
    setIsDeleting(true);
    try {
      await deletePortfolioHolding(parseInt(id));
      Alert.alert('Success', 'Holding deleted successfully', [
        { text: 'OK', onPress: () => router.back() },
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to delete holding. Please try again.');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  const calculateTotalValue = () => {
    const quantityNum = parseFloat(quantity) || 0;
    const priceNum = parseFloat(averagePrice) || 0;
    return quantityNum * priceNum;
  };

  if (!holding) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={[styles.loadingContainer, { backgroundColor: theme.colors.background }]}>
          <Text style={[styles.loadingText, { color: theme.colors.textSecondary }]}>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={[styles.header, { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border }]}>
          <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
            <Text style={[styles.cancelText, { color: theme.colors.textSecondary }]}>Cancel</Text>
          </TouchableOpacity>
          <Text style={[styles.title, { color: theme.colors.text }]}>Edit Holding</Text>
          <TouchableOpacity
            style={[styles.saveButton, { backgroundColor: theme.colors.primary }, isLoading && styles.disabledButton]}
            onPress={handleSave}
            disabled={isLoading}
          >
            <Text style={[styles.saveText, { color: theme.colors.surface }]}>Save</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.form}>
            <View style={[styles.stockInfo, { backgroundColor: theme.colors.surface }]}>
              <Text style={[styles.stockTicker, { color: theme.colors.text }]}>{holding.ticker}</Text>
              <Text style={[styles.stockLabel, { color: theme.colors.textSecondary }]}>Stock Ticker</Text>
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: theme.colors.text }]}>Quantity *</Text>
              <TextInput
                style={[styles.input, { backgroundColor: theme.colors.surface, color: theme.colors.text, borderColor: theme.colors.border }]}
                value={quantity}
                onChangeText={setQuantity}
                placeholder="Number of shares"
                placeholderTextColor={theme.colors.textSecondary}
                keyboardType="numeric"
              />
              <Text style={[styles.hint, { color: theme.colors.textSecondary }]}>Number of shares you own</Text>
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: theme.colors.text }]}>Average Price (₹) *</Text>
              <TextInput
                style={[styles.input, { backgroundColor: theme.colors.surface, color: theme.colors.text, borderColor: theme.colors.border }]}
                value={averagePrice}
                onChangeText={setAveragePrice}
                placeholder="Price per share"
                placeholderTextColor={theme.colors.textSecondary}
                keyboardType="decimal-pad"
              />
              <Text style={[styles.hint, { color: theme.colors.textSecondary }]}>Average price paid per share</Text>
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: theme.colors.text }]}>Purchase Date *</Text>
              <TextInput
                style={[styles.input, { backgroundColor: theme.colors.surface, color: theme.colors.text, borderColor: theme.colors.border }]}
                value={purchaseDate}
                onChangeText={setPurchaseDate}
                placeholder="YYYY-MM-DD"
                placeholderTextColor={theme.colors.textSecondary}
                keyboardType="numeric"
              />
              <Text style={[styles.hint, { color: theme.colors.textSecondary }]}>Date when you purchased the stock</Text>
            </View>

            {quantity && averagePrice && (
              <View style={[styles.summaryCard, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}>
                <Text style={[styles.summaryTitle, { color: theme.colors.text }]}>Investment Summary</Text>
                <View style={styles.summaryRow}>
                  <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>Total Shares</Text>
                  <Text style={[styles.summaryValue, { color: theme.colors.text }]}>{quantity}</Text>
                </View>
                <View style={styles.summaryRow}>
                  <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>Average Price</Text>
                  <Text style={[styles.summaryValue, { color: theme.colors.text }]}>₹{parseFloat(averagePrice).toFixed(2)}</Text>
                </View>
                <View style={styles.summaryRow}>
                  <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>Total Investment</Text>
                  <Text style={[styles.summaryValue, styles.totalValue, { color: theme.colors.primary }]}>
                    ₹{calculateTotalValue().toFixed(2)}
                  </Text>
                </View>
              </View>
            )}

            <TouchableOpacity
              style={[styles.deleteButton, { backgroundColor: theme.colors.error }, isDeleting && styles.disabledButton]}
              onPress={handleDelete}
              disabled={isDeleting}
            >
              <Ionicons name="trash-outline" size={20} color={theme.colors.surface} />
              <Text style={[styles.deleteButtonText, { color: theme.colors.surface }]}>Delete Holding</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  keyboardView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#8E8E93',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  cancelButton: {
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  cancelText: {
    fontSize: 16,
    color: '#007AFF',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  saveButton: {
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  disabledButton: {
    opacity: 0.5,
  },
  saveText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  form: {
    padding: 16,
  },
  stockInfo: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  stockTicker: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 4,
  },
  stockLabel: {
    fontSize: 14,
    color: '#8E8E93',
  },
  inputGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#000000',
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  hint: {
    fontSize: 14,
    color: '#8E8E93',
    marginTop: 4,
  },
  summaryCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 16,
    color: '#8E8E93',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000000',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: '600',
    color: '#007AFF',
  },
  deleteButton: {
    backgroundColor: '#FF3B30',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  deleteButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
