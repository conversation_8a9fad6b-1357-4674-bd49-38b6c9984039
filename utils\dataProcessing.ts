import moment from 'moment';
import { IPOData, StockData } from '../services/api';
import { PortfolioHolding } from '../services/database';

export interface ProcessedIPO extends IPOData {
  isOpen: boolean;
  daysRemaining: number;
  status: 'upcoming' | 'open' | 'closed';
  formattedOpenDate: string;
  formattedCloseDate: string;
  offerPriceNumber: number;
}

export interface ProcessedStock extends StockData {
  formattedLTP: string;
  formattedChange: string;
  changeColor: string;
  formattedVolume: string;
  formattedAmount: string;
}

export interface PortfolioHoldingWithMetrics extends PortfolioHolding {
  currentPrice: number;
  currentValue: number;
  profitLoss: number;
  profitLossPercentage: number;
  formattedCurrentValue: string;
  formattedProfitLoss: string;
  profitLossColor: string;
}

export interface StockGainersLosers {
  gainers: ProcessedStock[];
  losers: ProcessedStock[];
}

// IPO Processing Functions
export const processIPOData = (ipo: IPOData): ProcessedIPO => {
  const now = moment();
  const openDate = ipo.open_date ? moment(ipo.open_date) : null;
  const closeDate = ipo.close_date ? moment(ipo.close_date) : null;

  let status: 'upcoming' | 'open' | 'closed' = 'upcoming';
  let isOpen = false;
  let daysRemaining = 0;

  if (openDate && closeDate) {
    if (now.isBefore(openDate)) {
      status = 'upcoming';
    } else if (now.isAfter(closeDate)) {
      status = 'closed';
    } else {
      status = 'open';
      isOpen = true;
      daysRemaining = closeDate.diff(now, 'days');
    }
  }

  return {
    ...ipo,
    isOpen,
    daysRemaining,
    status,
    formattedOpenDate: openDate ? openDate.format('MMM DD, YYYY') : 'TBD',
    formattedCloseDate: closeDate ? closeDate.format('MMM DD, YYYY') : 'TBD',
    offerPriceNumber: parseFloat(ipo.offer_price) || 0,
  };
};

export const processIPOList = (ipos: IPOData[]): ProcessedIPO[] => {
  return ipos.map(processIPOData);
};

export const filterOpenIPOs = (ipos: ProcessedIPO[]): ProcessedIPO[] => {
  return ipos.filter(ipo => ipo.isOpen);
};

export const filterUpcomingIPOs = (ipos: ProcessedIPO[]): ProcessedIPO[] => {
  return ipos.filter(ipo => ipo.status === 'upcoming');
};

// Stock Processing Functions
export const processStockData = (stock: StockData): ProcessedStock => {
  const changeColor = stock.point_change >= 0 ? '#4CAF50' : '#F44336';
  
  return {
    ...stock,
    formattedLTP: formatCurrency(stock.ltp),
    formattedChange: `${stock.point_change >= 0 ? '+' : ''}${stock.point_change.toFixed(2)} (${stock.percentage_change >= 0 ? '+' : ''}${stock.percentage_change.toFixed(2)}%)`,
    changeColor,
    formattedVolume: formatNumber(stock.volume),
    formattedAmount: formatCurrency(stock.amount),
  };
};

export const processStockList = (stocks: StockData[]): ProcessedStock[] => {
  return stocks.map(processStockData);
};

export const getTopGainersAndLosers = (stocks: ProcessedStock[], count: number = 5): StockGainersLosers => {
  const sortedByChange = [...stocks].sort((a, b) => b.percentage_change - a.percentage_change);
  
  return {
    gainers: sortedByChange.slice(0, count),
    losers: sortedByChange.slice(-count).reverse(),
  };
};

export const searchStocks = (stocks: ProcessedStock[], query: string): ProcessedStock[] => {
  if (!query.trim()) return stocks;
  
  const searchTerm = query.toLowerCase().trim();
  
  return stocks.filter(stock => 
    stock.ticker.toLowerCase().includes(searchTerm) ||
    stock.ticker_name.toLowerCase().includes(searchTerm)
  );
};

// Portfolio Processing Functions
export const processPortfolioHolding = (
  holding: PortfolioHolding, 
  currentPrice: number
): PortfolioHoldingWithMetrics => {
  const currentValue = holding.quantity * currentPrice;
  const totalCost = holding.quantity * holding.averagePrice;
  const profitLoss = currentValue - totalCost;
  const profitLossPercentage = totalCost > 0 ? (profitLoss / totalCost) * 100 : 0;
  const profitLossColor = profitLoss >= 0 ? '#4CAF50' : '#F44336';

  return {
    ...holding,
    currentPrice,
    currentValue,
    profitLoss,
    profitLossPercentage,
    formattedCurrentValue: formatCurrency(currentValue),
    formattedProfitLoss: `${profitLoss >= 0 ? '+' : ''}${formatCurrency(profitLoss)} (${profitLoss >= 0 ? '+' : ''}${profitLossPercentage.toFixed(2)}%)`,
    profitLossColor,
  };
};

export const calculatePortfolioSummary = (holdings: PortfolioHoldingWithMetrics[]) => {
  const totalValue = holdings.reduce((sum, holding) => sum + holding.currentValue, 0);
  const totalCost = holdings.reduce((sum, holding) => sum + (holding.quantity * holding.averagePrice), 0);
  const totalProfitLoss = totalValue - totalCost;
  const totalProfitLossPercentage = totalCost > 0 ? (totalProfitLoss / totalCost) * 100 : 0;

  return {
    totalValue,
    totalCost,
    totalProfitLoss,
    totalProfitLossPercentage,
    holdingsCount: holdings.length,
    formattedTotalValue: formatCurrency(totalValue),
    formattedTotalCost: formatCurrency(totalCost),
    formattedTotalProfitLoss: `${totalProfitLoss >= 0 ? '+' : ''}${formatCurrency(totalProfitLoss)} (${totalProfitLoss >= 0 ? '+' : ''}${totalProfitLossPercentage.toFixed(2)}%)`,
    profitLossColor: totalProfitLoss >= 0 ? '#4CAF50' : '#F44336',
  };
};

// Utility Functions
export const formatCurrency = (amount: number): string => {
  if (amount >= 10000000) { // 1 crore
    return `₹${(amount / 10000000).toFixed(2)}Cr`;
  } else if (amount >= 100000) { // 1 lakh
    return `₹${(amount / 100000).toFixed(2)}L`;
  } else if (amount >= 1000) { // 1 thousand
    return `₹${(amount / 1000).toFixed(2)}K`;
  } else {
    return `₹${amount.toFixed(2)}`;
  }
};

export const formatNumber = (num: number): string => {
  if (num >= 10000000) {
    return `${(num / 10000000).toFixed(2)}Cr`;
  } else if (num >= 100000) {
    return `${(num / 100000).toFixed(2)}L`;
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(2)}K`;
  } else {
    return num.toString();
  }
};

export const formatDate = (dateString: string): string => {
  return moment(dateString).format('MMM DD, YYYY');
};

export const formatDateTime = (dateString: string): string => {
  return moment(dateString).format('MMM DD, YYYY HH:mm');
};

export const getRelativeTime = (dateString: string): string => {
  return moment(dateString).fromNow();
};

export const isMarketOpen = (): boolean => {
  const now = moment();
  const dayOfWeek = now.day(); // 0 = Sunday, 6 = Saturday
  const hour = now.hour();
  const minute = now.minute();
  const timeInMinutes = hour * 60 + minute;

  // Market is closed on weekends
  if (dayOfWeek === 0 || dayOfWeek === 6) {
    return false;
  }

  // Market hours: 9:15 AM to 3:30 PM (IST)
  const marketOpen = 9 * 60 + 15; // 9:15 AM
  const marketClose = 15 * 60 + 30; // 3:30 PM

  return timeInMinutes >= marketOpen && timeInMinutes <= marketClose;
};

export const getMarketStatus = (): { isOpen: boolean; message: string } => {
  const isOpen = isMarketOpen();
  const now = moment();
  
  if (isOpen) {
    return {
      isOpen: true,
      message: 'Market is open',
    };
  }

  const dayOfWeek = now.day();
  if (dayOfWeek === 0 || dayOfWeek === 6) {
    return {
      isOpen: false,
      message: 'Market closed (Weekend)',
    };
  }

  const hour = now.hour();
  if (hour < 9 || (hour === 9 && now.minute() < 15)) {
    return {
      isOpen: false,
      message: 'Market opens at 9:15 AM',
    };
  } else {
    return {
      isOpen: false,
      message: 'Market closed at 3:30 PM',
    };
  }
};

// Validation Functions
export const validateStockTicker = (ticker: string): boolean => {
  return /^[A-Z]{2,10}$/.test(ticker.trim());
};

export const validatePrice = (price: number): boolean => {
  return price > 0 && price < 1000000; // Max price 10 lakh
};

export const validateQuantity = (quantity: number): boolean => {
  return quantity > 0 && quantity <= 1000000; // Max quantity 10 lakh shares
};
