import AsyncStorage from '@react-native-async-storage/async-storage';
import { IPOData, StockData } from './api';

// Storage keys
const STORAGE_KEYS = {
  IPOS_ONGOING: 'ipos_ongoing',
  IPOS_UPCOMING: 'ipos_upcoming',
  APPLIED_IPOS: 'applied_ipos',
  WATCHLIST: 'watchlist',
  PRICE_ALERTS: 'price_alerts',
  SETTINGS: 'settings',
  LAST_FETCH_TIME: 'last_fetch_time',
} as const;

export interface AppliedIPO {
  finid: number;
  appliedAt: string;
}

export interface WatchlistItem {
  ticker: string;
  addedAt: string;
}

export interface PriceAlert {
  id: string;
  ticker: string;
  targetPrice: number;
  condition: 'above' | 'below';
  type: 'one-time' | 'recurring';
  triggered: boolean;
  createdAt: string;
}

export interface AppSettings {
  notificationsEnabled: boolean;
  ipoNotificationTime1: string; // "09:00"
  ipoNotificationTime2: string; // "20:00"
  priceAlertNotifications: boolean;
  backgroundRefreshEnabled: boolean;
}

class StorageService {
  // Generic storage methods
  private async setItem<T>(key: string, value: T): Promise<void> {
    try {
      await AsyncStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error(`Failed to save ${key}:`, error);
      throw error;
    }
  }

  private async getItem<T>(key: string): Promise<T | null> {
    try {
      const value = await AsyncStorage.getItem(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error(`Failed to get ${key}:`, error);
      return null;
    }
  }

  private async removeItem(key: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(key);
    } catch (error) {
      console.error(`Failed to remove ${key}:`, error);
      throw error;
    }
  }

  // IPO storage methods
  async saveOngoingIPOs(ipos: IPOData[]): Promise<void> {
    await this.setItem(STORAGE_KEYS.IPOS_ONGOING, ipos);
  }

  async getOngoingIPOs(): Promise<IPOData[]> {
    return (await this.getItem<IPOData[]>(STORAGE_KEYS.IPOS_ONGOING)) || [];
  }

  async saveUpcomingIPOs(ipos: IPOData[]): Promise<void> {
    await this.setItem(STORAGE_KEYS.IPOS_UPCOMING, ipos);
  }

  async getUpcomingIPOs(): Promise<IPOData[]> {
    return (await this.getItem<IPOData[]>(STORAGE_KEYS.IPOS_UPCOMING)) || [];
  }

  // Applied IPOs
  async addAppliedIPO(finid: number): Promise<void> {
    const appliedIPOs = await this.getAppliedIPOs();
    const newAppliedIPO: AppliedIPO = {
      finid,
      appliedAt: new Date().toISOString(),
    };
    
    const updatedAppliedIPOs = appliedIPOs.filter(ipo => ipo.finid !== finid);
    updatedAppliedIPOs.push(newAppliedIPO);
    
    await this.setItem(STORAGE_KEYS.APPLIED_IPOS, updatedAppliedIPOs);
  }

  async removeAppliedIPO(finid: number): Promise<void> {
    const appliedIPOs = await this.getAppliedIPOs();
    const updatedAppliedIPOs = appliedIPOs.filter(ipo => ipo.finid !== finid);
    await this.setItem(STORAGE_KEYS.APPLIED_IPOS, updatedAppliedIPOs);
  }

  async getAppliedIPOs(): Promise<AppliedIPO[]> {
    return (await this.getItem<AppliedIPO[]>(STORAGE_KEYS.APPLIED_IPOS)) || [];
  }

  async isIPOApplied(finid: number): Promise<boolean> {
    const appliedIPOs = await this.getAppliedIPOs();
    return appliedIPOs.some(ipo => ipo.finid === finid);
  }

  // Watchlist methods
  async addToWatchlist(ticker: string): Promise<void> {
    const watchlist = await this.getWatchlist();
    const newItem: WatchlistItem = {
      ticker,
      addedAt: new Date().toISOString(),
    };
    
    const updatedWatchlist = watchlist.filter(item => item.ticker !== ticker);
    updatedWatchlist.push(newItem);
    
    await this.setItem(STORAGE_KEYS.WATCHLIST, updatedWatchlist);
  }

  async removeFromWatchlist(ticker: string): Promise<void> {
    const watchlist = await this.getWatchlist();
    const updatedWatchlist = watchlist.filter(item => item.ticker !== ticker);
    await this.setItem(STORAGE_KEYS.WATCHLIST, updatedWatchlist);
  }

  async getWatchlist(): Promise<WatchlistItem[]> {
    return (await this.getItem<WatchlistItem[]>(STORAGE_KEYS.WATCHLIST)) || [];
  }

  async isInWatchlist(ticker: string): Promise<boolean> {
    const watchlist = await this.getWatchlist();
    return watchlist.some(item => item.ticker === ticker);
  }

  // Price alerts methods
  async addPriceAlert(alert: Omit<PriceAlert, 'id' | 'createdAt'>): Promise<string> {
    const alerts = await this.getPriceAlerts();
    const newAlert: PriceAlert = {
      ...alert,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      createdAt: new Date().toISOString(),
    };
    
    alerts.push(newAlert);
    await this.setItem(STORAGE_KEYS.PRICE_ALERTS, alerts);
    return newAlert.id;
  }

  async removePriceAlert(alertId: string): Promise<void> {
    const alerts = await this.getPriceAlerts();
    const updatedAlerts = alerts.filter(alert => alert.id !== alertId);
    await this.setItem(STORAGE_KEYS.PRICE_ALERTS, updatedAlerts);
  }

  async updatePriceAlert(alertId: string, updates: Partial<PriceAlert>): Promise<void> {
    const alerts = await this.getPriceAlerts();
    const updatedAlerts = alerts.map(alert => 
      alert.id === alertId ? { ...alert, ...updates } : alert
    );
    await this.setItem(STORAGE_KEYS.PRICE_ALERTS, updatedAlerts);
  }

  async getPriceAlerts(): Promise<PriceAlert[]> {
    return (await this.getItem<PriceAlert[]>(STORAGE_KEYS.PRICE_ALERTS)) || [];
  }

  async getPriceAlertsForTicker(ticker: string): Promise<PriceAlert[]> {
    const alerts = await this.getPriceAlerts();
    return alerts.filter(alert => alert.ticker === ticker);
  }

  // Settings methods
  async saveSettings(settings: AppSettings): Promise<void> {
    await this.setItem(STORAGE_KEYS.SETTINGS, settings);
  }

  async getSettings(): Promise<AppSettings> {
    const defaultSettings: AppSettings = {
      notificationsEnabled: true,
      ipoNotificationTime1: '09:00',
      ipoNotificationTime2: '20:00',
      priceAlertNotifications: true,
      backgroundRefreshEnabled: true,
    };
    
    const settings = await this.getItem<AppSettings>(STORAGE_KEYS.SETTINGS);
    return settings || defaultSettings;
  }

  // Last fetch time
  async setLastFetchTime(timestamp: string): Promise<void> {
    await this.setItem(STORAGE_KEYS.LAST_FETCH_TIME, timestamp);
  }

  async getLastFetchTime(): Promise<string | null> {
    return await this.getItem<string>(STORAGE_KEYS.LAST_FETCH_TIME);
  }

  // Clear all data
  async clearAllData(): Promise<void> {
    const keys = Object.values(STORAGE_KEYS);
    await Promise.all(keys.map(key => this.removeItem(key)));
  }
}

export const storageService = new StorageService();
