import { getStockChangeColor, useTheme } from '@/contexts/ThemeContext';
import { useAppStore } from '@/store/appStore';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React from 'react';
import { RefreshControl, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function PortfolioScreen() {
  const {
    portfolioHoldings,
    portfolioSummary,
    isRefreshing,
    refreshAllData,
  } = useAppStore();

  const { theme } = useTheme();

  const handleAddHolding = () => {
    router.push('/add-holding');
  };

  const handleEditHolding = (id: number) => {
    router.push(`/edit-holding/${id}`);
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={[styles.header, { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border }]}>
        <Text style={[styles.title, { color: theme.colors.text }]}>Portfolio</Text>
        <TouchableOpacity style={[styles.addButton, { backgroundColor: theme.colors.primary }]} onPress={handleAddHolding}>
          <Ionicons name="add" size={24} color={theme.colors.surface} />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={refreshAllData} />
        }
      >
        {portfolioSummary && (
          <View style={[styles.summaryCard, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.summaryTitle, { color: theme.colors.text }]}>Portfolio Summary</Text>
            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>Total Value</Text>
              <Text style={[styles.summaryValue, { color: theme.colors.text }]}>{portfolioSummary.formattedTotalValue}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>Total Cost</Text>
              <Text style={[styles.summaryValue, { color: theme.colors.text }]}>{portfolioSummary.formattedTotalCost}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>P&L</Text>
              <Text style={[styles.summaryValue, { color: getStockChangeColor(portfolioSummary.totalProfitLoss, theme) }]}>
                {portfolioSummary.formattedTotalProfitLoss}
              </Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>Holdings</Text>
              <Text style={[styles.summaryValue, { color: theme.colors.text }]}>{portfolioSummary.holdingsCount}</Text>
            </View>
          </View>
        )}

        {portfolioHoldings.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="briefcase-outline" size={64} color={theme.colors.textSecondary} />
            <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>No Holdings Yet</Text>
            <Text style={[styles.emptySubtitle, { color: theme.colors.textSecondary }]}>
              Add your first stock holding to start tracking your portfolio
            </Text>
            <TouchableOpacity style={[styles.emptyButton, { backgroundColor: theme.colors.primary }]} onPress={handleAddHolding}>
              <Text style={[styles.emptyButtonText, { color: theme.colors.surface }]}>Add Holding</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.holdingsList}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Holdings</Text>
            {portfolioHoldings.map((holding) => (
              <TouchableOpacity
                key={holding.id}
                style={[styles.holdingCard, { backgroundColor: theme.colors.surface }]}
                onPress={() => holding.id && handleEditHolding(holding.id)}
              >
                <View style={styles.holdingHeader}>
                  <Text style={[styles.holdingTicker, { color: theme.colors.text }]}>{holding.ticker}</Text>
                  <Text style={[styles.holdingPL, { color: getStockChangeColor(holding.profitLoss, theme) }]}>
                    {holding.formattedProfitLoss}
                  </Text>
                </View>
                <View style={styles.holdingDetails}>
                  <View style={styles.holdingDetailRow}>
                    <Text style={[styles.holdingDetailLabel, { color: theme.colors.textSecondary }]}>Quantity</Text>
                    <Text style={[styles.holdingDetailValue, { color: theme.colors.text }]}>{holding.quantity}</Text>
                  </View>
                  <View style={styles.holdingDetailRow}>
                    <Text style={[styles.holdingDetailLabel, { color: theme.colors.textSecondary }]}>Avg Price</Text>
                    <Text style={[styles.holdingDetailValue, { color: theme.colors.text }]}>₹{holding.averagePrice.toFixed(2)}</Text>
                  </View>
                  <View style={styles.holdingDetailRow}>
                    <Text style={[styles.holdingDetailLabel, { color: theme.colors.textSecondary }]}>Current Price</Text>
                    <Text style={[styles.holdingDetailValue, { color: theme.colors.text }]}>₹{holding.currentPrice.toFixed(2)}</Text>
                  </View>
                  <View style={styles.holdingDetailRow}>
                    <Text style={[styles.holdingDetailLabel, { color: theme.colors.textSecondary }]}>Current Value</Text>
                    <Text style={[styles.holdingDetailValue, { color: theme.colors.text }]}>{holding.formattedCurrentValue}</Text>
                  </View>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
  },
  addButton: {
    backgroundColor: '#007AFF',
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  summaryCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 16,
    color: '#8E8E93',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#000000',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
    marginBottom: 24,
    paddingHorizontal: 32,
  },
  emptyButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  holdingsList: {
    marginTop: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 12,
  },
  holdingCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  holdingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  holdingTicker: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  holdingPL: {
    fontSize: 16,
    fontWeight: '600',
  },
  holdingDetails: {
    gap: 8,
  },
  holdingDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  holdingDetailLabel: {
    fontSize: 14,
    color: '#8E8E93',
  },
  holdingDetailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#000000',
  },
});
