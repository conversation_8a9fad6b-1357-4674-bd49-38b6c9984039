import * as SQLite from 'expo-sqlite';

export interface PortfolioHolding {
  id?: number;
  ticker: string;
  quantity: number;
  averagePrice: number;
  purchaseDate: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface PortfolioSummary {
  totalValue: number;
  totalCost: number;
  totalProfitLoss: number;
  totalProfitLossPercentage: number;
  holdingsCount: number;
}

class DatabaseService {
  private db: SQLite.SQLiteDatabase | null = null;

  async initializeDatabase(): Promise<void> {
    try {
      this.db = await SQLite.openDatabaseAsync('portfolio.db');
      await this.createTables();
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const createPortfolioTable = `
      CREATE TABLE IF NOT EXISTS portfolio_holdings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        ticker TEXT NOT NULL,
        quantity REAL NOT NULL,
        average_price REAL NOT NULL,
        purchase_date TEXT NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      );
    `;

    const createIndexes = `
      CREATE INDEX IF NOT EXISTS idx_portfolio_ticker ON portfolio_holdings(ticker);
      CREATE INDEX IF NOT EXISTS idx_portfolio_purchase_date ON portfolio_holdings(purchase_date);
    `;

    await this.db.execAsync(createPortfolioTable);
    await this.db.execAsync(createIndexes);
  }

  async addHolding(holding: Omit<PortfolioHolding, 'id' | 'createdAt' | 'updatedAt'>): Promise<number> {
    if (!this.db) throw new Error('Database not initialized');

    const query = `
      INSERT INTO portfolio_holdings (ticker, quantity, average_price, purchase_date)
      VALUES (?, ?, ?, ?)
    `;

    const result = await this.db.runAsync(query, [
      holding.ticker,
      holding.quantity,
      holding.averagePrice,
      holding.purchaseDate,
    ]);

    return result.lastInsertRowId;
  }

  async updateHolding(id: number, updates: Partial<PortfolioHolding>): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const fields = [];
    const values = [];

    if (updates.quantity !== undefined) {
      fields.push('quantity = ?');
      values.push(updates.quantity);
    }
    if (updates.averagePrice !== undefined) {
      fields.push('average_price = ?');
      values.push(updates.averagePrice);
    }
    if (updates.purchaseDate !== undefined) {
      fields.push('purchase_date = ?');
      values.push(updates.purchaseDate);
    }

    if (fields.length === 0) return;

    fields.push('updated_at = CURRENT_TIMESTAMP');
    values.push(id);

    const query = `
      UPDATE portfolio_holdings 
      SET ${fields.join(', ')}
      WHERE id = ?
    `;

    await this.db.runAsync(query, values);
  }

  async deleteHolding(id: number): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const query = 'DELETE FROM portfolio_holdings WHERE id = ?';
    await this.db.runAsync(query, [id]);
  }

  async getHolding(id: number): Promise<PortfolioHolding | null> {
    if (!this.db) throw new Error('Database not initialized');

    const query = `
      SELECT id, ticker, quantity, average_price as averagePrice, 
             purchase_date as purchaseDate, created_at as createdAt, 
             updated_at as updatedAt
      FROM portfolio_holdings 
      WHERE id = ?
    `;

    const result = await this.db.getFirstAsync<PortfolioHolding>(query, [id]);
    return result || null;
  }

  async getAllHoldings(): Promise<PortfolioHolding[]> {
    if (!this.db) throw new Error('Database not initialized');

    const query = `
      SELECT id, ticker, quantity, average_price as averagePrice, 
             purchase_date as purchaseDate, created_at as createdAt, 
             updated_at as updatedAt
      FROM portfolio_holdings 
      ORDER BY created_at DESC
    `;

    const result = await this.db.getAllAsync<PortfolioHolding>(query);
    return result;
  }

  async getHoldingsByTicker(ticker: string): Promise<PortfolioHolding[]> {
    if (!this.db) throw new Error('Database not initialized');

    const query = `
      SELECT id, ticker, quantity, average_price as averagePrice, 
             purchase_date as purchaseDate, created_at as createdAt, 
             updated_at as updatedAt
      FROM portfolio_holdings 
      WHERE ticker = ?
      ORDER BY purchase_date DESC
    `;

    const result = await this.db.getAllAsync<PortfolioHolding>(query, [ticker]);
    return result;
  }

  async getTotalQuantityForTicker(ticker: string): Promise<number> {
    if (!this.db) throw new Error('Database not initialized');

    const query = `
      SELECT COALESCE(SUM(quantity), 0) as total
      FROM portfolio_holdings 
      WHERE ticker = ?
    `;

    const result = await this.db.getFirstAsync<{ total: number }>(query, [ticker]);
    return result?.total || 0;
  }

  async getAveragePrice(ticker: string): Promise<number> {
    if (!this.db) throw new Error('Database not initialized');

    const query = `
      SELECT 
        COALESCE(SUM(quantity * average_price) / SUM(quantity), 0) as weightedAverage
      FROM portfolio_holdings 
      WHERE ticker = ?
    `;

    const result = await this.db.getFirstAsync<{ weightedAverage: number }>(query, [ticker]);
    return result?.weightedAverage || 0;
  }

  async getUniqueTickersInPortfolio(): Promise<string[]> {
    if (!this.db) throw new Error('Database not initialized');

    const query = `
      SELECT DISTINCT ticker
      FROM portfolio_holdings 
      ORDER BY ticker
    `;

    const result = await this.db.getAllAsync<{ ticker: string }>(query);
    return result.map(row => row.ticker);
  }

  async clearAllHoldings(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const query = 'DELETE FROM portfolio_holdings';
    await this.db.runAsync(query);
  }

  async getPortfolioStats(): Promise<{
    totalHoldings: number;
    uniqueStocks: number;
    oldestHolding: string | null;
    newestHolding: string | null;
  }> {
    if (!this.db) throw new Error('Database not initialized');

    const query = `
      SELECT 
        COUNT(*) as totalHoldings,
        COUNT(DISTINCT ticker) as uniqueStocks,
        MIN(purchase_date) as oldestHolding,
        MAX(purchase_date) as newestHolding
      FROM portfolio_holdings
    `;

    const result = await this.db.getFirstAsync<{
      totalHoldings: number;
      uniqueStocks: number;
      oldestHolding: string | null;
      newestHolding: string | null;
    }>(query);

    return result || {
      totalHoldings: 0,
      uniqueStocks: 0,
      oldestHolding: null,
      newestHolding: null,
    };
  }

  async closeDatabase(): Promise<void> {
    if (this.db) {
      await this.db.closeAsync();
      this.db = null;
    }
  }
}

export const databaseService = new DatabaseService();
