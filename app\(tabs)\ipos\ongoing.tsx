import { useTheme } from '@/contexts/ThemeContext';
import { useAppStore } from '@/store/appStore';
import { ProcessedIPO } from '@/utils/dataProcessing';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { Alert, RefreshControl, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function OngoingIPOsScreen() {
  const {
    ongoingIPOs,
    appliedIPOs,
    isRefreshing,
    refreshAllData,
    markIPOAsApplied,
    unmarkIPOAsApplied,
  } = useAppStore();

  const { theme } = useTheme();

  const handleApplyToggle = async (ipo: ProcessedIPO) => {
    try {
      if (appliedIPOs.has(ipo.finid)) {
        await unmarkIPOAsApplied(ipo.finid);
        Alert.alert('Success', `Removed ${ipo.company_name} from applied IPOs`);
      } else {
        await markIPOAsApplied(ipo.finid);
        Alert.alert('Success', `Marked ${ipo.company_name} as applied`);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to update IPO status');
    }
  };

  const renderIPOCard = (ipo: ProcessedIPO) => {
    const isApplied = appliedIPOs.has(ipo.finid);
    const daysLeft = ipo.daysRemaining;

    return (
      <View key={ipo.finid} style={[styles.ipoCard, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}>
        <View style={styles.ipoHeader}>
          <View style={styles.ipoTitleSection}>
            <Text style={[styles.ipoCompanyName, { color: theme.colors.text }]}>{ipo.company_name}</Text>
            <Text style={[styles.ipoSector, { color: theme.colors.textSecondary }]}>{ipo.Sector}</Text>
          </View>
          <View style={styles.ipoStatusSection}>
            {daysLeft > 0 ? (
              <View style={[styles.statusBadge, styles.openBadge, { backgroundColor: theme.colors.warning + '20' }]}>
                <Text style={[styles.statusText, { color: theme.colors.warning }]}>
                  {daysLeft === 1 ? 'Last Day' : `${daysLeft} days left`}
                </Text>
              </View>
            ) : (
              <View style={[styles.statusBadge, styles.closingSoonBadge, { backgroundColor: theme.colors.error + '20' }]}>
                <Text style={[styles.statusText, { color: theme.colors.error }]}>Closing Soon</Text>
              </View>
            )}
          </View>
        </View>

        <View style={styles.ipoDetails}>
          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: theme.colors.textSecondary }]}>Offer Price</Text>
            <Text style={[styles.detailValue, { color: theme.colors.text }]}>₹{ipo.offerPriceNumber}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: theme.colors.textSecondary }]}>Shares Offered</Text>
            <Text style={[styles.detailValue, { color: theme.colors.text }]}>{ipo.shares_offered.toLocaleString()}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: theme.colors.textSecondary }]}>Issue Manager</Text>
            <Text style={[styles.detailValue, { color: theme.colors.text }]}>{ipo.issue_manager}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: theme.colors.textSecondary }]}>Open Date</Text>
            <Text style={[styles.detailValue, { color: theme.colors.text }]}>{ipo.formattedOpenDate}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: theme.colors.textSecondary }]}>Close Date</Text>
            <Text style={[styles.detailValue, { color: theme.colors.text }]}>{ipo.formattedCloseDate}</Text>
          </View>
        </View>

        <View style={styles.ipoActions}>
          <TouchableOpacity
            style={[
              styles.applyButton,
              isApplied
                ? [styles.appliedButton, { backgroundColor: theme.colors.success }]
                : [styles.notAppliedButton, { backgroundColor: theme.colors.primary, borderColor: theme.colors.primary }]
            ]}
            onPress={() => handleApplyToggle(ipo)}
          >
            <Ionicons
              name={isApplied ? "checkmark-circle" : "add-circle-outline"}
              size={20}
              color={theme.colors.surface}
            />
            <Text style={[
              styles.applyButtonText,
              isApplied
                ? [styles.appliedButtonText, { color: theme.colors.surface }]
                : [styles.notAppliedButtonText, { color: theme.colors.surface }]
            ]}>
              {isApplied ? 'Applied' : 'Mark as Applied'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={refreshAllData} />
        }
      >
        {ongoingIPOs.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="trending-up-outline" size={64} color={theme.colors.textSecondary} />
            <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>No Open IPOs</Text>
            <Text style={[styles.emptySubtitle, { color: theme.colors.textSecondary }]}>
              There are currently no IPOs open for application. Check back later or view upcoming IPOs.
            </Text>
          </View>
        ) : (
          <View style={styles.iposList}>
            <View style={styles.listHeader}>
              <Text style={[styles.listTitle, { color: theme.colors.text }]}>Open IPOs ({ongoingIPOs.length})</Text>
              <Text style={[styles.listSubtitle, { color: theme.colors.textSecondary }]}>
                IPOs currently open for application
              </Text>
            </View>
            {ongoingIPOs.map(renderIPOCard)}
          </View>
        )}

        <View style={[styles.infoCard, { backgroundColor: theme.colors.surface }]}>
          <Ionicons name="information-circle" size={24} color={theme.colors.primary} />
          <View style={styles.infoContent}>
            <Text style={[styles.infoTitle, { color: theme.colors.text }]}>IPO Application Tips</Text>
            <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
              • Apply early to avoid last-minute rush{'\n'}
              • Check your bank account balance{'\n'}
              • Keep your PAN and bank details ready{'\n'}
              • Review the company&apos;s financials before applying
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#000000',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
    paddingHorizontal: 32,
    lineHeight: 22,
  },
  iposList: {
    marginBottom: 16,
  },
  listHeader: {
    marginBottom: 16,
  },
  listTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  listSubtitle: {
    fontSize: 14,
    color: '#8E8E93',
  },
  ipoCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  ipoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  ipoTitleSection: {
    flex: 1,
    marginRight: 12,
  },
  ipoCompanyName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
    lineHeight: 24,
  },
  ipoSector: {
    fontSize: 14,
    color: '#8E8E93',
  },
  ipoStatusSection: {
    alignItems: 'flex-end',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  openBadge: {
    backgroundColor: '#34C759',
  },
  closingSoonBadge: {
    backgroundColor: '#FF9500',
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  ipoDetails: {
    marginBottom: 16,
    gap: 8,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: 14,
    color: '#8E8E93',
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#000000',
  },
  ipoActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  applyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 6,
  },
  notAppliedButton: {
    backgroundColor: '#F0F8FF',
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  appliedButton: {
    backgroundColor: '#34C759',
  },
  applyButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  notAppliedButtonText: {
    color: '#007AFF',
  },
  appliedButtonText: {
    color: '#FFFFFF',
  },
  infoCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoContent: {
    flex: 1,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#8E8E93',
    lineHeight: 20,
  },
});
