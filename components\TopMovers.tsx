import { Colors } from '@/constants/Colors';
import { useTheme } from '@/contexts/ThemeContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Ionicons } from "@expo/vector-icons";
import { router } from 'expo-router';
import React, { useState } from "react";
import {
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from "react-native";

type Stock = {
    ticker: string;
    ticker_name: string;
    formattedLTP: string;
    formattedChange: string;
    formattedVolume: string;
    formattedAmount: string;
    point_change: number;
    changeColor: string;
};

type Props = {
    gainers: Stock[];
    losers: Stock[];
};

export default function TopMovers({ gainers, losers }: Props) {
    const [activeTab, setActiveTab] = useState<"gainers" | "losers">("gainers");
    const colorScheme = useColorScheme() ?? 'light';
    const { theme } = useTheme();

    // Theme colors
    const backgroundColor = useThemeColor({ light: Colors.light.background, dark: Colors.dark.background }, 'background');
    const textColor = useThemeColor({ light: Colors.light.text, dark: Colors.dark.text }, 'text');
    const tint = useThemeColor({ light: Colors.light.tint, dark: Colors.dark.tint }, 'tint');

    const data = activeTab === "gainers" ? gainers : losers;

    // Add press handler
    const handleStockPress = (stock: Stock) => {
        router.push(`/stock-details/${stock.ticker}`);
    };

    // Render rows without FlatList
    const renderRows = () => {
        return data.slice(0, 10).map((item) => (
            <TouchableOpacity
                key={item.ticker}
                onPress={() => handleStockPress(item)}
                activeOpacity={0.7}
            >
                <View style={[styles.row, {
                    backgroundColor: theme.colors.surface,
                    borderColor: theme.colors.border
                }]}>
                    <View style={[styles.iconContainer, {
                        backgroundColor: theme.colors.border
                    }]}>
                        <Ionicons
                            name={activeTab === "gainers" ? "trending-up" : "trending-down"}
                            size={22}
                            color={item.changeColor}
                        />
                    </View>
                    <View style={{ flex: 1 }}>
                        <Text style={[styles.ticker, { color: theme.colors.text }]}>{item.ticker}</Text>
                        <Text style={[styles.name, { color: theme.colors.textSecondary }]} numberOfLines={1}>
                            {item.ticker_name}
                        </Text>
                    </View>
                    <View style={{ alignItems: "flex-end" }}>
                        <Text style={[styles.ltp, { color: theme.colors.text }]}>{item.formattedLTP}</Text>
                        <Text style={[styles.change, { color: item.changeColor }]}>
                            {item.formattedChange}
                        </Text>
                    </View>
                </View>
            </TouchableOpacity>
        ));
    };

    return (
        <View style={[styles.section, {
            backgroundColor: theme.colors.surface,
            borderColor: theme.colors.border
        }]}>
            {/* Tabs */}
            <View style={styles.tabs}>
                <TouchableOpacity
                    onPress={() => setActiveTab("gainers")}
                    style={[
                        styles.chip,
                        activeTab === "gainers" && {
                            backgroundColor: tint,
                        }
                    ]}
                >
                    <Text
                        style={[
                            styles.chipText,
                            {
                                color: activeTab === "gainers" ? theme.colors.surface : theme.colors.text
                            }
                        ]}
                    >
                        Top Gainers
                    </Text>
                </TouchableOpacity>
                <TouchableOpacity
                    onPress={() => setActiveTab("losers")}
                    style={[
                        styles.chip,
                        activeTab === "losers" && {
                            backgroundColor: tint,
                        }
                    ]}
                >
                    <Text
                        style={[
                            styles.chipText,
                            {
                                color: activeTab === "losers" ? theme.colors.surface : theme.colors.text
                            }
                        ]}
                    >
                        Top Losers
                    </Text>
                </TouchableOpacity>
            </View>

            {/* List */}
            {renderRows()}
        </View>
    );
}

const styles = StyleSheet.create({
    section: {
        marginBottom: 20,
        padding: 16,
        borderRadius: 12,
        borderWidth: 1,
    },
    tabs: {
        flexDirection: "row",
        marginBottom: 16,
        justifyContent: 'center',
        alignItems: 'center',
    },
    chip: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        marginHorizontal: 6,
        backgroundColor: 'transparent',
        borderWidth: 1,
        borderColor: 'transparent',
    },
    chipText: {
        fontSize: 14,
        fontWeight: "500",
        textAlign: 'center',
    },
    row: {
        flexDirection: "row",
        alignItems: "center",
        padding: 12,
        borderRadius: 12,
        marginBottom: 8,
        borderWidth: 1,
    },
    iconContainer: {
        width: 32,
        height: 32,
        borderRadius: 16,
        alignItems: "center",
        justifyContent: "center",
        marginRight: 10,
    },
    ticker: {
        fontSize: 16,
        fontWeight: "600"
    },
    name: {
        fontSize: 12,
        marginTop: 2
    },
    ltp: {
        fontSize: 14,
        fontWeight: "600"
    },
    change: {
        fontSize: 12,
        marginTop: 2,
        fontWeight: "500"
    },
});