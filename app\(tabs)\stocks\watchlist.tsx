import { useTheme } from '@/contexts/ThemeContext';
import { useAppStore } from '@/store/appStore';
import { ProcessedStock } from '@/utils/dataProcessing';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useMemo } from 'react';
import { Alert, FlatList, RefreshControl, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function WatchlistScreen() {
  const {
    stocks,
    watchlist,
    isRefreshing,
    refreshAllData,
    removeFromWatchlist,
  } = useAppStore();

  const { theme } = useTheme();

  const watchlistStocks = useMemo(() => {
    const watchlistTickers = new Set(watchlist.map(item => item.ticker));
    return stocks.filter(stock => watchlistTickers.has(stock.ticker));
  }, [stocks, watchlist]);

  const handleStockPress = (stock: ProcessedStock) => {
    router.push(`/stock-details/${stock.ticker}`);
  };

  const handleRemoveFromWatchlist = async (ticker: string) => {
    Alert.alert(
      'Remove from Watchlist',
      `Are you sure you want to remove ${ticker} from your watchlist?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              await removeFromWatchlist(ticker);
            } catch (error) {
              Alert.alert('Error', 'Failed to remove from watchlist');
            }
          },
        },
      ]
    );
  };

  const handlePriceAlert = (ticker: string) => {
    router.push(`/price-alert/${ticker}`);
  };

  const renderStockItem = ({ item: stock }: { item: ProcessedStock }) => {
    return (
      <TouchableOpacity style={styles.stockCard} onPress={() => handleStockPress(stock)}>
        <View style={styles.stockHeader}>
          <View style={styles.stockInfo}>
            <Text style={styles.stockTicker}>{stock.ticker}</Text>
            <Text style={styles.stockName} numberOfLines={2}>
              {stock.ticker_name}
            </Text>
          </View>
          <View style={styles.stockActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handlePriceAlert(stock.ticker)}
            >
              <Ionicons name="notifications-outline" size={20} color="#007AFF" />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.actionButton, styles.removeButton]}
              onPress={() => handleRemoveFromWatchlist(stock.ticker)}
            >
              <Ionicons name="close" size={20} color="#FF3B30" />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.stockPricing}>
          <View style={styles.priceSection}>
            <Text style={styles.currentPrice}>{stock.formattedLTP}</Text>
            <Text style={[styles.priceChange, { color: stock.changeColor }]}>
              {stock.formattedChange}
            </Text>
          </View>
          <View style={styles.trendIndicator}>
            <Ionicons
              name={stock.percentage_change >= 0 ? "trending-up" : "trending-down"}
              size={24}
              color={stock.changeColor}
            />
          </View>
        </View>

        <View style={styles.stockDetails}>
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Open</Text>
            <Text style={styles.detailValue}>₹{stock.open.toFixed(2)}</Text>
          </View>
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>High</Text>
            <Text style={styles.detailValue}>₹{stock.high.toFixed(2)}</Text>
          </View>
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Low</Text>
            <Text style={styles.detailValue}>₹{stock.low.toFixed(2)}</Text>
          </View>
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Volume</Text>
            <Text style={styles.detailValue}>{stock.formattedVolume}</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.titleSection}>
        <Ionicons name="star" size={24} color="#FFD700" />
        <Text style={styles.title}>My Watchlist</Text>
      </View>
      <Text style={styles.subtitle}>
        Stocks you're keeping an eye on
      </Text>
      <View style={styles.statsContainer}>
        <Text style={styles.statsText}>
          {watchlistStocks.length} stocks in watchlist
        </Text>
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="star-outline" size={64} color="#8E8E93" />
      <Text style={styles.emptyTitle}>Your Watchlist is Empty</Text>
      <Text style={styles.emptySubtitle}>
        Add stocks to your watchlist to track their performance. You can add stocks from the "All Stocks" tab.
      </Text>
      <TouchableOpacity
        style={styles.browseButton}
        onPress={() => router.push('/(tabs)/stocks/stocks-list')}
      >
        <Text style={styles.browseButtonText}>Browse Stocks</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <FlatList
        data={watchlistStocks}
        renderItem={renderStockItem}
        keyExtractor={(item) => item.ticker}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={refreshAllData} />
        }
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  listContainer: {
    padding: 16,
  },
  header: {
    marginBottom: 20,
  },
  titleSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
  },
  subtitle: {
    fontSize: 16,
    color: '#8E8E93',
    marginBottom: 12,
  },
  statsContainer: {
    alignItems: 'center',
    backgroundColor: '#FFF8E1',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    alignSelf: 'flex-start',
  },
  statsText: {
    fontSize: 14,
    color: '#FF9500',
    fontWeight: '600',
  },
  stockCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderLeftWidth: 4,
    borderLeftColor: '#FFD700',
  },
  stockHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  stockInfo: {
    flex: 1,
    marginRight: 12,
  },
  stockTicker: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  stockName: {
    fontSize: 14,
    color: '#8E8E93',
    lineHeight: 18,
  },
  stockActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#F2F2F7',
  },
  removeButton: {
    backgroundColor: '#FFE8E8',
  },
  stockPricing: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  priceSection: {
    flex: 1,
  },
  currentPrice: {
    fontSize: 20,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  priceChange: {
    fontSize: 16,
    fontWeight: '500',
  },
  trendIndicator: {
    marginLeft: 12,
  },
  stockDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#F2F2F7',
  },
  detailItem: {
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: 12,
    color: '#8E8E93',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#000000',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#000000',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
    paddingHorizontal: 32,
    lineHeight: 22,
    marginBottom: 24,
  },
  browseButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  browseButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
