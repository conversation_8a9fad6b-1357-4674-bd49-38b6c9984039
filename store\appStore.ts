import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { apiService } from '../services/api';
import { backgroundTaskService } from '../services/backgroundTasks';
import { databaseService, PortfolioHolding } from '../services/database';
import { notificationService } from '../services/notifications';
import { AppSettings, PriceAlert, storageService, WatchlistItem } from '../services/storage';
import { calculatePortfolioSummary, PortfolioHoldingWithMetrics, ProcessedIPO, ProcessedStock, processIPOList, processPortfolioHolding, processStockList } from '../utils/dataProcessing';

export interface AppState {
  // Loading states
  isLoading: boolean;
  isRefreshing: boolean;
  
  // IPO data
  ongoingIPOs: ProcessedIPO[];
  upcomingIPOs: ProcessedIPO[];
  appliedIPOs: Set<number>;
  
  // Stock data
  stocks: ProcessedStock[];
  watchlist: WatchlistItem[];
  priceAlerts: PriceAlert[];
  
  // Portfolio data
  portfolioHoldings: PortfolioHoldingWithMetrics[];
  portfolioSummary: any;
  
  // App settings
  settings: AppSettings;
  
  // Error state
  error: string | null;
  
  // Last update time
  lastUpdateTime: string | null;
}

export interface AppActions {
  // Initialization
  initializeApp: () => Promise<void>;
  
  // Data fetching
  fetchIPOData: (skipNotifications?: boolean) => Promise<void>;
  fetchStockData: () => Promise<void>;
  refreshAllData: () => Promise<void>;
  
  // IPO actions
  markIPOAsApplied: (finid: number) => Promise<void>;
  unmarkIPOAsApplied: (finid: number) => Promise<void>;
  
  // Watchlist actions
  addToWatchlist: (ticker: string) => Promise<void>;
  removeFromWatchlist: (ticker: string) => Promise<void>;
  
  // Price alert actions
  addPriceAlert: (alert: Omit<PriceAlert, 'id' | 'createdAt'>) => Promise<string>;
  removePriceAlert: (alertId: string) => Promise<void>;
  updatePriceAlert: (alertId: string, updates: Partial<PriceAlert>) => Promise<void>;
  
  // Portfolio actions
  addPortfolioHolding: (holding: Omit<PortfolioHolding, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updatePortfolioHolding: (id: number, updates: Partial<PortfolioHolding>) => Promise<void>;
  deletePortfolioHolding: (id: number) => Promise<void>;
  refreshPortfolio: () => Promise<void>;
  
  // Settings actions
  updateSettings: (settings: Partial<AppSettings>) => Promise<void>;
  
  // Utility actions
  setError: (error: string | null) => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  setRefreshing: (refreshing: boolean) => void;
}

type AppStore = AppState & AppActions;

export const useAppStore = create<AppStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    isLoading: false,
    isRefreshing: false,
    ongoingIPOs: [],
    upcomingIPOs: [],
    appliedIPOs: new Set(),
    stocks: [],
    watchlist: [],
    priceAlerts: [],
    portfolioHoldings: [],
    portfolioSummary: null,
    settings: {
      notificationsEnabled: true,
      ipoNotificationTime1: '09:00',
      ipoNotificationTime2: '20:00',
      priceAlertNotifications: true,
      backgroundRefreshEnabled: true,
    },
    error: null,
    lastUpdateTime: null,

    // Actions
    initializeApp: async () => {
      try {
        set({ isLoading: true, error: null });

        // Initialize services
        await databaseService.initializeDatabase();
        await notificationService.initialize();
        await backgroundTaskService.initialize();

        // Load stored data
        const [settings, watchlist, priceAlerts, appliedIPOs] = await Promise.all([
          storageService.getSettings(),
          storageService.getWatchlist(),
          storageService.getPriceAlerts(),
          storageService.getAppliedIPOs(),
        ]);

        set({
          settings,
          watchlist,
          priceAlerts,
          appliedIPOs: new Set(appliedIPOs.map(ipo => ipo.finid)),
        });

        // Fetch fresh data
        await get().refreshAllData();

      } catch (error) {
        console.error('App initialization failed:', error);
        set({ error: 'Failed to initialize app' });
      } finally {
        set({ isLoading: false });
      }
    },

    fetchIPOData: async (skipNotifications = false) => {
      try {
        const [ongoingIPOs, upcomingIPOs] = await Promise.all([
          apiService.getOngoingIPOs(),
          apiService.getUpcomingIPOs(),
        ]);

        const processedOngoing = processIPOList(ongoingIPOs);
        const processedUpcoming = processIPOList(upcomingIPOs);

        // Check if ongoing IPOs have actually changed
        const currentOngoing = get().ongoingIPOs;
        const hasOngoingChanged = JSON.stringify(currentOngoing) !== JSON.stringify(processedOngoing);

        set({
          ongoingIPOs: processedOngoing,
          upcomingIPOs: processedUpcoming,
        });

        // Save to storage
        await Promise.all([
          storageService.saveOngoingIPOs(ongoingIPOs),
          storageService.saveUpcomingIPOs(upcomingIPOs),
        ]);

        // Only reschedule notifications if ongoing IPOs have changed and not skipping notifications
        if (hasOngoingChanged && !skipNotifications) {
          await notificationService.scheduleIPOReminders(ongoingIPOs);
        }

      } catch (error) {
        console.error('Failed to fetch IPO data:', error);
        // Load from storage as fallback
        const [storedOngoing, storedUpcoming] = await Promise.all([
          storageService.getOngoingIPOs(),
          storageService.getUpcomingIPOs(),
        ]);

        set({
          ongoingIPOs: processIPOList(storedOngoing),
          upcomingIPOs: processIPOList(storedUpcoming),
        });
      }
    },

    fetchStockData: async () => {
      try {
        const stockData = await apiService.getStockTickers();
        const processedStocks = processStockList(stockData);

        set({ stocks: processedStocks });

        // Check price alerts
        await backgroundTaskService.checkPriceAlertsManually();

      } catch (error) {
        console.error('Failed to fetch stock data:', error);
        set({ error: 'Failed to fetch stock data' });
      }
    },

    refreshAllData: async () => {
      try {
        set({ isRefreshing: true, error: null });

        await Promise.all([
          get().fetchIPOData(true), // Skip notifications during manual refresh
          get().fetchStockData(),
          get().refreshPortfolio(),
        ]);

        set({ lastUpdateTime: new Date().toISOString() });
        await storageService.setLastFetchTime(new Date().toISOString());

      } catch (error) {
        console.error('Failed to refresh data:', error);
        set({ error: 'Failed to refresh data' });
      } finally {
        set({ isRefreshing: false });
      }
    },

    markIPOAsApplied: async (finid: number) => {
      try {
        await storageService.addAppliedIPO(finid);
        const appliedIPOs = get().appliedIPOs;
        appliedIPOs.add(finid);
        set({ appliedIPOs: new Set(appliedIPOs) });

        // Reschedule notifications only for ongoing IPOs
        const ongoingIPOs = get().ongoingIPOs;
        await notificationService.scheduleIPOReminders(ongoingIPOs);

      } catch (error) {
        console.error('Failed to mark IPO as applied:', error);
        set({ error: 'Failed to update IPO status' });
      }
    },

    unmarkIPOAsApplied: async (finid: number) => {
      try {
        await storageService.removeAppliedIPO(finid);
        const appliedIPOs = get().appliedIPOs;
        appliedIPOs.delete(finid);
        set({ appliedIPOs: new Set(appliedIPOs) });

        // Reschedule notifications only for ongoing IPOs
        const ongoingIPOs = get().ongoingIPOs;
        await notificationService.scheduleIPOReminders(ongoingIPOs);

      } catch (error) {
        console.error('Failed to unmark IPO:', error);
        set({ error: 'Failed to update IPO status' });
      }
    },

    addToWatchlist: async (ticker: string) => {
      try {
        await storageService.addToWatchlist(ticker);
        const watchlist = await storageService.getWatchlist();
        set({ watchlist });
      } catch (error) {
        console.error('Failed to add to watchlist:', error);
        set({ error: 'Failed to add to watchlist' });
      }
    },

    removeFromWatchlist: async (ticker: string) => {
      try {
        await storageService.removeFromWatchlist(ticker);
        const watchlist = await storageService.getWatchlist();
        set({ watchlist });
      } catch (error) {
        console.error('Failed to remove from watchlist:', error);
        set({ error: 'Failed to remove from watchlist' });
      }
    },

    addPriceAlert: async (alert: Omit<PriceAlert, 'id' | 'createdAt'>) => {
      try {
        const alertId = await storageService.addPriceAlert(alert);
        const priceAlerts = await storageService.getPriceAlerts();
        set({ priceAlerts });
        return alertId;
      } catch (error) {
        console.error('Failed to add price alert:', error);
        set({ error: 'Failed to add price alert' });
        throw error;
      }
    },

    removePriceAlert: async (alertId: string) => {
      try {
        await storageService.removePriceAlert(alertId);
        const priceAlerts = await storageService.getPriceAlerts();
        set({ priceAlerts });
      } catch (error) {
        console.error('Failed to remove price alert:', error);
        set({ error: 'Failed to remove price alert' });
      }
    },

    updatePriceAlert: async (alertId: string, updates: Partial<PriceAlert>) => {
      try {
        await storageService.updatePriceAlert(alertId, updates);
        const priceAlerts = await storageService.getPriceAlerts();
        set({ priceAlerts });
      } catch (error) {
        console.error('Failed to update price alert:', error);
        set({ error: 'Failed to update price alert' });
      }
    },

    addPortfolioHolding: async (holding: Omit<PortfolioHolding, 'id' | 'createdAt' | 'updatedAt'>) => {
      try {
        await databaseService.addHolding(holding);
        await get().refreshPortfolio();
      } catch (error) {
        console.error('Failed to add portfolio holding:', error);
        set({ error: 'Failed to add portfolio holding' });
      }
    },

    updatePortfolioHolding: async (id: number, updates: Partial<PortfolioHolding>) => {
      try {
        await databaseService.updateHolding(id, updates);
        await get().refreshPortfolio();
      } catch (error) {
        console.error('Failed to update portfolio holding:', error);
        set({ error: 'Failed to update portfolio holding' });
      }
    },

    deletePortfolioHolding: async (id: number) => {
      try {
        await databaseService.deleteHolding(id);
        await get().refreshPortfolio();
      } catch (error) {
        console.error('Failed to delete portfolio holding:', error);
        set({ error: 'Failed to delete portfolio holding' });
      }
    },

    refreshPortfolio: async () => {
      try {
        const holdings = await databaseService.getAllHoldings();
        const stocks = get().stocks;
        
        const stockMap = new Map(stocks.map(stock => [stock.ticker, stock]));
        
        const holdingsWithMetrics = holdings.map(holding => {
          const stock = stockMap.get(holding.ticker);
          const currentPrice = stock?.ltp || holding.averagePrice;
          return processPortfolioHolding(holding, currentPrice);
        });

        const portfolioSummary = calculatePortfolioSummary(holdingsWithMetrics);

        set({
          portfolioHoldings: holdingsWithMetrics,
          portfolioSummary,
        });

      } catch (error) {
        console.error('Failed to refresh portfolio:', error);
        set({ error: 'Failed to refresh portfolio' });
      }
    },

    updateSettings: async (newSettings: Partial<AppSettings>) => {
      try {
        const currentSettings = get().settings;
        const updatedSettings = { ...currentSettings, ...newSettings };
        
        await storageService.saveSettings(updatedSettings);
        set({ settings: updatedSettings });

        // Reschedule notifications if notification settings changed
        if (newSettings.notificationsEnabled !== undefined ||
            newSettings.ipoNotificationTime1 ||
            newSettings.ipoNotificationTime2) {
          const ongoingIPOs = get().ongoingIPOs;
          await notificationService.scheduleIPOReminders(ongoingIPOs);
        }

      } catch (error) {
        console.error('Failed to update settings:', error);
        set({ error: 'Failed to update settings' });
      }
    },

    setError: (error: string | null) => set({ error }),
    clearError: () => set({ error: null }),
    setLoading: (loading: boolean) => set({ isLoading: loading }),
    setRefreshing: (refreshing: boolean) => set({ isRefreshing: refreshing }),
  }))
);
