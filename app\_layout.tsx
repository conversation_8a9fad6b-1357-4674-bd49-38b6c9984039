import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { StatusBar } from 'expo-status-bar';
import { useEffect } from 'react';
import { Alert } from 'react-native';
import 'react-native-reanimated';

import { ThemeProvider, useTheme } from '@/contexts/ThemeContext';
import { useAppStore } from '@/store/appStore';

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

function NavigationStack() {
  return (
    <Stack>
      <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
      <Stack.Screen name="stock-details/[ticker]" options={{ presentation: 'modal', headerShown: false }} />
      <Stack.Screen name="add-holding" options={{ presentation: 'modal',headerShown: false }} />
      <Stack.Screen name="edit-holding/[id]" options={{ presentation: 'modal', headerShown: false  }} />
      <Stack.Screen name="price-alert/[ticker]" options={{ presentation: 'modal', headerShown: false  }} />
      <Stack.Screen name="+not-found" />
    </Stack>
  );
}

function AppContent() {
  const initializeApp = useAppStore((state) => state.initializeApp);
  const isLoading = useAppStore((state) => state.isLoading);
  const error = useAppStore((state) => state.error);
  const { theme } = useTheme();

  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  useEffect(() => {
    if (loaded) {
      initializeApp().finally(() => {
        SplashScreen.hideAsync();
      });
    }
  }, [loaded, initializeApp]);

  useEffect(() => {
    if (error) {
      Alert.alert('Error', error);
    }
  }, [error]);

  if (!loaded || isLoading || !theme) {
    return null;
  }

  return <NavigationStack />;
}

function ThemedStatusBar() {
  const { isDark } = useTheme();
  return <StatusBar style={isDark ? "light" : "dark"} />;
}

export default function RootLayout() {
  return (
    <ThemeProvider>
      <AppContent />
      <ThemedStatusBar />
    </ThemeProvider>
  );
}
