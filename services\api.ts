import { Alert } from "react-native";
const BASE_URL = "https://nepse-data-y5f1.onrender.com";

export interface IPOData {
  company_name: string;
  finid: number;
  Sector: string;
  type: string;
  shares_offered: number;
  issue_manager: string;
  offer_price: string;
  offered_to: string;
  application_date: string;
  approval_date: string;
  open_date: string | null;
  close_date: string | null;
  Status: string;
  filing_date: string;
}

export interface StockData {
  ticker: string;
  indices: string;
  ticker_name: string;
  ltp: number;
  ltv: number;
  point_change: number;
  percentage_change: number;
  open: number;
  high: number;
  low: number;
  volume: number;
  previousClosing: number;
  calculated_on: string;
  amount: number;
  datasource: string;
  icon: string;
}

// News interface added
export interface NewsItem {
  image: string;
  link: string;
  title: string;
  nepali_date: string;
  excerpt: string;
}

// Updated interface to match the actual API response
export interface APIResponse<T> {
  success: boolean;
  type: string;
  data: T[];
}

class APIService {
  private async makeRequest<T>(endpoint: string): Promise<T> {
    try {
      // Proper timeout implementation using AbortController
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const response = await fetch(`${BASE_URL}${endpoint}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  async getOngoingIPOs(): Promise<IPOData[]> {
    try {
      const data = await this.makeRequest<APIResponse<IPOData>>(
        "/api/ipos/ongoing"
      );
      return data.data || [];
    } catch (error) {
      console.error("Failed to fetch ongoing IPOs:", error);
      Alert.alert(
        "Error",
        "Failed to fetch ongoing IPOs. Please check your connection."
      );
      return [];
    }
  }

  async getUpcomingIPOs(): Promise<IPOData[]> {
    try {
      const data = await this.makeRequest<APIResponse<IPOData>>(
        "/api/ipos/upcoming"
      );
      return data.data || []; // Changed from data.response to data.data
    } catch (error) {
      console.error("Failed to fetch upcoming IPOs:", error);
      Alert.alert(
        "Error",
        "Failed to fetch upcoming IPOs. Please check your connection."
      );
      return [];
    }
  }

  async getStockTickers(): Promise<StockData[]> {
    try {
      const data = await this.makeRequest<APIResponse<StockData>>(
        "/api/tickers"
      );
      // Filter out stocks with indices "Mutual Fund" or "Promoter" in ticker_name
      const filteredStocks = data.data.filter(
        (stock) =>
          stock.indices !== "Mutual Fund" &&
          !stock.ticker_name.includes("Promoter")
      );
      return filteredStocks || [];
    } catch (error) {
      console.error("Failed to fetch stock tickers:", error);
      Alert.alert(
        "Error",
        "Failed to fetch stock data. Please check your connection."
      );
      return [];
    }
  }

  async getStockByTicker(ticker: string): Promise<StockData | null> {
    try {
      const stocks = await this.getStockTickers();
      return stocks.find((stock) => stock.ticker === ticker) || null;
    } catch (error) {
      console.error(`Failed to fetch stock data for ${ticker}:`, error);
      return null;
    }
  }

    // New method for fetching news
  async getNews(): Promise<NewsItem[]> {
    try {
      const data = await this.makeRequest<NewsItem>("/api/news");
      return data?.data || [];
    } catch (error) {
      console.error("Failed to fetch news:", error);
      Alert.alert(
        "Error",
        "Failed to fetch news. Please check your connection."
      );
      return [];
    }
  }

}



export const apiService = new APIService();
