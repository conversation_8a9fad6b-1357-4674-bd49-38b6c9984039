import { useTheme } from '@/contexts/ThemeContext';
import { MaterialTopTabNavigationOptions, createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { withLayoutContext } from 'expo-router';
import { View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { Navigator } = createMaterialTopTabNavigator();

export const MaterialTopTabs = withLayoutContext<
  MaterialTopTabNavigationOptions,
  typeof Navigator
>(Navigator);

export default function IPOTabsLayout() {
  const insets = useSafeAreaInsets();
  const { theme } = useTheme();

  return (
    <View style={{ flex: 1, paddingTop: insets.top }}>
      <MaterialTopTabs
        screenOptions={{
          tabBarActiveTintColor: theme.colors.primary,
          tabBarInactiveTintColor: theme.colors.textSecondary,
          tabBarIndicatorStyle: {
            backgroundColor: theme.colors.primary,
          },
          tabBarStyle: {
            backgroundColor: theme.colors.surface,
          },
          tabBarLabelStyle: {
            fontSize: 14,
            fontWeight: '600',
          },
        }}>
      <MaterialTopTabs.Screen
        name="ongoing"
        options={{
          title: 'Open IPOs',
        }}
      />
      <MaterialTopTabs.Screen
        name="upcoming"
        options={{
          title: 'Upcoming IPOs',
        }}
      />
    </MaterialTopTabs>
    </View>
  );
}
