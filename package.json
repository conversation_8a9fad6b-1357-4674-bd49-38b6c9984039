{"name": "finapp", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/datetimepicker": "^8.4.4", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/material-top-tabs": "^7.3.7", "@react-navigation/native": "^7.1.6", "expo": "~53.0.22", "expo-background-fetch": "^13.1.6", "expo-blur": "~14.1.5", "expo-constants": "~17.1.7", "expo-device": "^7.1.4", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-linking": "~7.1.7", "expo-notifications": "^0.31.4", "expo-router": "~5.1.5", "expo-splash-screen": "~0.30.10", "expo-sqlite": "^15.2.14", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.11", "expo-task-manager": "^13.1.6", "expo-web-browser": "~14.2.0", "moment": "^2.30.1", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.6", "react-native-gesture-handler": "~2.24.0", "react-native-gifted-charts": "^1.4.64", "react-native-linear-gradient": "^2.8.3", "react-native-pager-view": "^6.9.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.12.1", "react-native-tab-view": "^4.1.3", "react-native-vector-icons": "^10.3.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "zustand": "^5.0.8"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}