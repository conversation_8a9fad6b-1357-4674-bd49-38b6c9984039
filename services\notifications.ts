import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { IPOData, apiService } from './api';
import { storageService } from './storage';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

export interface ScheduledNotification {
  id: string;
  type: 'ipo_reminder' | 'price_alert';
  scheduledTime: string;
  content: {
    title: string;
    body: string;
    data?: any;
  };
}

class NotificationService {
  private notificationListener: any = null;
  private responseListener: any = null;
  private lastScheduleTime = 0;
  private scheduleDebounceMs = 5000; // 5 seconds debounce

  async initialize(): Promise<boolean> {
    try {
      // Request permissions
      const permission = await this.requestPermissions();
      if (!permission) {
        console.log('Notification permissions not granted');
        return false;
      }

      // Set up notification listeners
      this.setupListeners();

      // Configure notification channel for Android
      if (Platform.OS === 'android') {
        await this.setupAndroidChannel();
      }

      console.log('Notification service initialized successfully');
      return true;
    } catch (error) {
      console.error('Failed to initialize notification service:', error);
      return false;
    }
  }

  private async requestPermissions(): Promise<boolean> {
    if (!Device.isDevice) {
      console.log('Must use physical device for notifications');
      return false;
    }

    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    return finalStatus === 'granted';
  }

  private setupListeners(): void {
    // Listener for notifications received while app is foregrounded
    this.notificationListener = Notifications.addNotificationReceivedListener(notification => {
      console.log('Notification received:', notification);
    });

    // Listener for when user taps on notification
    this.responseListener = Notifications.addNotificationResponseReceivedListener(response => {
      console.log('Notification response:', response);
      this.handleNotificationResponse(response);
    });
  }

  private async setupAndroidChannel(): Promise<void> {
    await Notifications.setNotificationChannelAsync('default', {
      name: 'Default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });

    await Notifications.setNotificationChannelAsync('ipo-alerts', {
      name: 'IPO Alerts',
      importance: Notifications.AndroidImportance.HIGH,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });

    await Notifications.setNotificationChannelAsync('price-alerts', {
      name: 'Price Alerts',
      importance: Notifications.AndroidImportance.HIGH,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }

  private handleNotificationResponse(response: Notifications.NotificationResponse): void {
    const data = response.notification.request.content.data;
    
    if (data?.type === 'ipo_reminder') {
      // Navigate to IPO screen
      console.log('Navigate to IPO screen');
    } else if (data?.type === 'price_alert') {
      // Navigate to stock details
      console.log('Navigate to stock details for:', data?.ticker);
    }
  }

  async scheduleIPOReminders(ongoingIPOs: IPOData[]): Promise<void> {
    try {
      // Debounce rapid successive calls (e.g., during refresh)
      const now = Date.now();
      if (now - this.lastScheduleTime < this.scheduleDebounceMs) {
        console.log('Skipping notification scheduling due to debounce');
        return;
      }
      this.lastScheduleTime = now;

      // Cancel existing IPO notifications
      await this.cancelIPONotifications();

      const settings = await storageService.getSettings();
      if (!settings.notificationsEnabled) {
        return;
      }

      const appliedIPOs = await storageService.getAppliedIPOs();
      const appliedFinids = new Set(appliedIPOs.map(ipo => ipo.finid));

      // Filter out applied IPOs and closed IPOs from ongoing IPOs only
      const activeOngoingIPOs = ongoingIPOs.filter(ipo => {
        if (appliedFinids.has(ipo.finid)) return false;

        // Check if IPO is currently open (has open_date and close_date)
        if (!ipo.open_date || !ipo.close_date) return false;

        const now = new Date();
        const openDate = new Date(ipo.open_date);
        const closeDate = new Date(ipo.close_date);

        // Only include if IPO is currently open
        return now >= openDate && now <= closeDate;
      });

      if (activeOngoingIPOs.length === 0) {
        return;
      }

      // Schedule notifications for both times
      await this.scheduleIPONotificationForTime(activeOngoingIPOs, settings.ipoNotificationTime1);
      await this.scheduleIPONotificationForTime(activeOngoingIPOs, settings.ipoNotificationTime2);

    } catch (error) {
      console.error('Failed to schedule IPO reminders:', error);
    }
  }

  private async scheduleIPONotificationForTime(ipos: IPOData[], time: string): Promise<void> {
    const [hours, minutes] = time.split(':').map(Number);
    const now = new Date();
    const scheduledTime = new Date();
    scheduledTime.setHours(hours, minutes, 0, 0);

    // If the time has passed today, schedule for tomorrow
    if (scheduledTime <= now) {
      scheduledTime.setDate(scheduledTime.getDate() + 1);
    }

    const content = this.generateIPONotificationContent(ipos);

    await Notifications.scheduleNotificationAsync({
      content: {
        title: content.title,
        body: content.body,
        data: { type: 'ipo_reminder', ipos: ipos.map(ipo => ipo.finid) },
      },
      trigger: {
        date: scheduledTime,
        repeats: true,
      },
    });
  }

  private generateIPONotificationContent(ipos: IPOData[]): { title: string; body: string } {
    if (ipos.length === 0) {
      return {
        title: 'IPO Alert',
        body: 'No active IPOs at the moment',
      };
    }

    if (ipos.length === 1) {
      const ipo = ipos[0];
      const daysLeft = this.calculateDaysLeft(ipo.close_date);
      return {
        title: 'IPO Reminder',
        body: `${ipo.company_name} IPO ${daysLeft > 0 ? `closes in ${daysLeft} days` : 'is closing soon'}`,
      };
    }

    return {
      title: 'IPO Reminders',
      body: `${ipos.length} active IPOs available for application`,
    };
  }

  private calculateDaysLeft(closeDate: string | null): number {
    if (!closeDate) return 0;
    
    const close = new Date(closeDate);
    const now = new Date();
    const diffTime = close.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return Math.max(0, diffDays);
  }

  async cancelIPONotifications(): Promise<void> {
    try {
      const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
      const ipoNotifications = scheduledNotifications.filter(
        notification => notification.content.data?.type === 'ipo_reminder'
      );

      for (const notification of ipoNotifications) {
        await Notifications.cancelScheduledNotificationAsync(notification.identifier);
      }
    } catch (error) {
      console.error('Failed to cancel IPO notifications:', error);
    }
  }

  async sendPriceAlert(ticker: string, currentPrice: number, targetPrice: number, condition: 'above' | 'below'): Promise<void> {
    try {
      const settings = await storageService.getSettings();
      if (!settings.priceAlertNotifications) {
        return;
      }

      const title = `Price Alert: ${ticker}`;
      const body = `${ticker} is now ${condition} ₹${targetPrice}! Current price: ₹${currentPrice}`;

      await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data: { 
            type: 'price_alert', 
            ticker, 
            currentPrice, 
            targetPrice, 
            condition 
          },
        },
        trigger: null, // Send immediately
      });
    } catch (error) {
      console.error('Failed to send price alert:', error);
    }
  }

  async cancelPriceAlert(alertId: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(alertId);
    } catch (error) {
      console.error('Failed to cancel price alert:', error);
    }
  }

  async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Failed to cancel all notifications:', error);
    }
  }

  async getScheduledNotifications(): Promise<Notifications.NotificationRequest[]> {
    try {
      return await Notifications.getAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Failed to get scheduled notifications:', error);
      return [];
    }
  }

  cleanup(): void {
    if (this.notificationListener) {
      Notifications.removeNotificationSubscription(this.notificationListener);
    }
    if (this.responseListener) {
      Notifications.removeNotificationSubscription(this.responseListener);
    }
  }
}

export const notificationService = new NotificationService();
