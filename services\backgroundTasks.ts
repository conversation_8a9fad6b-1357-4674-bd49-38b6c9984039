import * as TaskManager from 'expo-task-manager';
import * as BackgroundFetch from 'expo-background-fetch';
import { apiService, StockData } from './api';
import { storageService, PriceAlert } from './storage';
import { notificationService } from './notifications';

const PRICE_ALERT_TASK = 'price-alert-check';

// Define the background task
TaskManager.defineTask(PRICE_ALERT_TASK, async () => {
  try {
    console.log('Background task: Checking price alerts');
    
    // Get all active price alerts
    const alerts = await storageService.getPriceAlerts();
    const activeAlerts = alerts.filter(alert => !alert.triggered || alert.type === 'recurring');
    
    if (activeAlerts.length === 0) {
      return BackgroundFetch.BackgroundFetchResult.NoData;
    }

    // Get current stock data
    const stockData = await apiService.getStockTickers();
    if (stockData.length === 0) {
      return BackgroundFetch.BackgroundFetchResult.Failed;
    }

    // Create a map for quick lookup
    const stockMap = new Map<string, StockData>();
    stockData.forEach(stock => stockMap.set(stock.ticker, stock));

    let alertsTriggered = 0;

    // Check each alert
    for (const alert of activeAlerts) {
      const stock = stockMap.get(alert.ticker);
      if (!stock) continue;

      const currentPrice = stock.ltp;
      let shouldTrigger = false;

      if (alert.condition === 'above' && currentPrice >= alert.targetPrice) {
        shouldTrigger = true;
      } else if (alert.condition === 'below' && currentPrice <= alert.targetPrice) {
        shouldTrigger = true;
      }

      if (shouldTrigger) {
        // Send notification
        await notificationService.sendPriceAlert(
          alert.ticker,
          currentPrice,
          alert.targetPrice,
          alert.condition
        );

        // Update alert status
        if (alert.type === 'one-time') {
          await storageService.updatePriceAlert(alert.id, { triggered: true });
        }

        alertsTriggered++;
      }
    }

    console.log(`Background task completed: ${alertsTriggered} alerts triggered`);
    return alertsTriggered > 0 
      ? BackgroundFetch.BackgroundFetchResult.NewData 
      : BackgroundFetch.BackgroundFetchResult.NoData;

  } catch (error) {
    console.error('Background task failed:', error);
    return BackgroundFetch.BackgroundFetchResult.Failed;
  }
});

class BackgroundTaskService {
  async initialize(): Promise<boolean> {
    try {
      // Check if background fetch is available
      const isAvailable = await BackgroundFetch.getStatusAsync();
      if (isAvailable !== BackgroundFetch.BackgroundFetchStatus.Available) {
        console.log('Background fetch is not available');
        return false;
      }

      // Register the background task
      await BackgroundFetch.registerTaskAsync(PRICE_ALERT_TASK, {
        minimumInterval: 15 * 60, // 15 minutes minimum interval
        stopOnTerminate: false,
        startOnBoot: true,
      });

      console.log('Background task registered successfully');
      return true;
    } catch (error) {
      console.error('Failed to register background task:', error);
      return false;
    }
  }

  async unregisterTask(): Promise<void> {
    try {
      await BackgroundFetch.unregisterTaskAsync(PRICE_ALERT_TASK);
      console.log('Background task unregistered');
    } catch (error) {
      console.error('Failed to unregister background task:', error);
    }
  }

  async isTaskRegistered(): Promise<boolean> {
    try {
      const isRegistered = await TaskManager.isTaskRegisteredAsync(PRICE_ALERT_TASK);
      return isRegistered;
    } catch (error) {
      console.error('Failed to check task registration:', error);
      return false;
    }
  }

  async getBackgroundFetchStatus(): Promise<BackgroundFetch.BackgroundFetchStatus> {
    try {
      return await BackgroundFetch.getStatusAsync();
    } catch (error) {
      console.error('Failed to get background fetch status:', error);
      return BackgroundFetch.BackgroundFetchStatus.Denied;
    }
  }

  // Manual check for price alerts (when app comes to foreground)
  async checkPriceAlertsManually(): Promise<number> {
    try {
      console.log('Manual price alert check started');
      
      const alerts = await storageService.getPriceAlerts();
      const activeAlerts = alerts.filter(alert => !alert.triggered || alert.type === 'recurring');
      
      if (activeAlerts.length === 0) {
        return 0;
      }

      const stockData = await apiService.getStockTickers();
      if (stockData.length === 0) {
        throw new Error('Failed to fetch stock data');
      }

      const stockMap = new Map<string, StockData>();
      stockData.forEach(stock => stockMap.set(stock.ticker, stock));

      let alertsTriggered = 0;

      for (const alert of activeAlerts) {
        const stock = stockMap.get(alert.ticker);
        if (!stock) continue;

        const currentPrice = stock.ltp;
        let shouldTrigger = false;

        if (alert.condition === 'above' && currentPrice >= alert.targetPrice) {
          shouldTrigger = true;
        } else if (alert.condition === 'below' && currentPrice <= alert.targetPrice) {
          shouldTrigger = true;
        }

        if (shouldTrigger) {
          await notificationService.sendPriceAlert(
            alert.ticker,
            currentPrice,
            alert.targetPrice,
            alert.condition
          );

          if (alert.type === 'one-time') {
            await storageService.updatePriceAlert(alert.id, { triggered: true });
          }

          alertsTriggered++;
        }
      }

      console.log(`Manual check completed: ${alertsTriggered} alerts triggered`);
      return alertsTriggered;

    } catch (error) {
      console.error('Manual price alert check failed:', error);
      throw error;
    }
  }
}

export const backgroundTaskService = new BackgroundTaskService();
