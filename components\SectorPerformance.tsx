import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useThemeColor } from '@/hooks/useThemeColor';
import { MaterialIcons } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Dimensions, StyleSheet, Text, TouchableOpacity, View, ScrollView } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';

// Type definitions for sector data
interface SectorData {
    indices: string;
    turnover: number | null;
    percentage_change: number;
    points_change: number;
    advancers: number | null;
    decliners: number | null;
    latest_point: string;
    direction: string;
}
interface ApiResponse {
    success: boolean;
    type: string;
    data: SectorData[];
}

const SectorPerformance = () => {
    const [data, setData] = useState<SectorData[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const colorScheme = useColorScheme() ?? 'light';
    const { theme } = useTheme();
    
    // Theme colors
    const backgroundColor = useThemeColor({ light: Colors.light.background, dark: Colors.dark.background }, 'background');
    const textColor = useThemeColor({ light: Colors.light.text, dark: Colors.dark.text }, 'text');
    const tint = useThemeColor({ light: Colors.light.tint, dark: Colors.dark.tint }, 'tint');
    
    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);
                const response = await fetch('https://nepse-data-y5f1.onrender.com/api/sector-performance');
                const json: ApiResponse = await response.json();
                if (json.success && json.data) {
                    // Filter out NEPSE index
                    const filteredData = json.data.filter(item => item.indices !== "NEPSE");
                    setData(filteredData);
                } else {
                    setError("Invalid API response");
                }
            } catch (err) {
                setError("Failed to fetch sector data");
                console.error(err);
            } finally {
                setLoading(false);
            }
        };
        fetchData();
    }, []);

    const formatNumber = (num: number): string => {
        return num.toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    };

    const formatTurnover = (turnover: number | null): string => {
        if (turnover === null) return 'N/A';
        if (turnover >= 10000000) {
            return `${(turnover / 10000000).toFixed(2)} Cr`;
        } else if (turnover >= 100000) {
            return `${(turnover / 100000).toFixed(2)} Lac`;
        } else if (turnover >= 1000) {
            return `${(turnover / 1000).toFixed(2)} K`;
        }
        return formatNumber(turnover);
    };

    const formatPercentage = (num: number): string => {
        return `${num >= 0 ? '+' : ''}${num.toFixed(2)}%`;
    };

    const getChangeColor = (change: number): string => {
        return change >= 0 ? theme.colors.success : theme.colors.error;
    };

    // Render sector cards without FlatList
    const renderSectorCards = () => {
        return data.map((item) => (
            <TouchableOpacity
                key={item.indices}
                style={[styles.card, { 
                    backgroundColor: theme.colors.surface, 
                    borderColor: theme.colors.border 
                }]}
            >
                <View style={styles.cardHeader}>
                    <Text style={[styles.sectorName, { color: theme.colors.text }]}>{item.indices}</Text>
                    <View style={[styles.directionIndicator, { 
                        backgroundColor: getChangeColor(item.percentage_change) 
                    }]}>
                        <MaterialIcons
                            name={item.percentage_change >= 0 ? 'arrow-upward' : 'arrow-downward'}
                            size={14}
                            color={theme.colors.surface}
                        />
                    </View>
                </View>
                <Text style={[styles.sectorValue, { color: theme.colors.text }]}>
                    {formatNumber(parseFloat(item.latest_point))}
                </Text>
                <View style={styles.changeContainer}>
                    <Text style={[styles.pointChange, { 
                        color: getChangeColor(item.points_change) 
                    }]}>
                        {item.points_change >= 0 ? '+' : ''}{formatNumber(item.points_change)}
                    </Text>
                    <Text style={[styles.percentageChange, { 
                        color: getChangeColor(item.percentage_change) 
                    }]}>
                        ({formatPercentage(item.percentage_change)})
                    </Text>
                </View>
                <View style={[styles.statsContainer, { 
                    borderTopColor: theme.colors.border 
                }]}>
                    <View style={styles.statItem}>
                        <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                            Turnover
                        </Text>
                        <Text style={[styles.statValue, { color: theme.colors.text }]}>
                            {formatTurnover(item.turnover)}
                        </Text>
                    </View>
                    {item.advancers !== null && item.decliners !== null && (
                        <View style={styles.statItem}>
                            <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                                A/D
                            </Text>
                            <Text style={[styles.statValue, { color: theme.colors.text }]}>
                                {item.advancers}/{item.decliners}
                            </Text>
                        </View>
                    )}
                </View>
            </TouchableOpacity>
        ));
    };

    if (loading) {
        return (
            <View style={[styles.container, { backgroundColor: theme.colors.surface }]}>
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={tint} />
                    <Text style={[styles.loadingText, { color: theme.colors.textSecondary }]}>
                        Loading sector data...
                    </Text>
                </View>
            </View>
        );
    }

    if (error) {
        return (
            <View style={[styles.container, { backgroundColor: theme.colors.surface }]}>
                <View style={styles.errorContainer}>
                    <Text style={[styles.errorText, { color: theme.colors.error }]}>{error}</Text>
                </View>
            </View>
        );
    }

    return (
        <View style={[styles.container, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.title, { color: theme.colors.text }]}>Sector Performance</Text>
            <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.scrollContainer}
            >
                {renderSectorCards()}
            </ScrollView>
        </View>
    );
};

const { width: screenWidth } = Dimensions.get('window');
const cardWidth = screenWidth * 0.45; // Reduced to 45% of screen width for much smaller cards

const styles = StyleSheet.create({
    container: {
        borderRadius: 12,
        padding: 16,
        marginBottom: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    title: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 16,
    },
    loadingContainer: {
        height: 120,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingText: {
        marginTop: 8,
        fontSize: 16,
    },
    errorContainer: {
        height: 120,
        justifyContent: 'center',
        alignItems: 'center',
    },
    errorText: {
        fontSize: 16,
        textAlign: 'center',
    },
    scrollContainer: {
        paddingRight: 16,
    },
    card: {
        width: cardWidth,
        borderRadius: 12,
        padding: 6, // Reduced padding
        borderWidth: 1,
        marginRight: 12, // Add margin between cards
    },
    cardHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 6, // Reduced margin
    },
    sectorName: {
        fontSize: 14, // Reduced font size
        fontWeight: '600',
        flex: 1,
    },
    directionIndicator: {
        width: 20, // Reduced size
        height: 20, // Reduced size
        borderRadius: 10, // Adjusted to match new size
        justifyContent: 'center',
        alignItems: 'center',
    },
    directionText: {
        fontSize: 12,
        fontWeight: 'bold',
        color: 'white',
    },
    sectorValue: {
        fontSize: 16, // Reduced font size
        fontWeight: 'bold',
        marginBottom: 4, // Reduced margin
    },
    changeContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 6, // Reduced margin
    },
    pointChange: {
        fontSize: 12, // Reduced font size
        fontWeight: '500',
        marginRight: 6, // Reduced margin
    },
    percentageChange: {
        fontSize: 11, // Reduced font size
        fontWeight: '500',
    },
    statsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingTop: 4, // Reduced padding
        borderTopWidth: 1,
    },
    statItem: {
        alignItems: 'center',
    },
    statLabel: {
        fontSize: 10, // Reduced font size
        marginBottom: 1, // Reduced margin
    },
    statValue: {
        fontSize: 11, // Reduced font size
        fontWeight: '500',
    },
});

export default SectorPerformance;