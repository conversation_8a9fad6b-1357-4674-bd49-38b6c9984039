// screens/StockListScreen.tsx
import { getStockChangeColor, useTheme } from '@/contexts/ThemeContext';
import { useAppStore } from '@/store/appStore';
import { ProcessedStock, searchStocks } from '@/utils/dataProcessing';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Alert, FlatList, RefreshControl, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Simple debounce implementation
function debounce<T extends (...args: any[]) => any>(func: T, delay: number): T {
  let timeoutId: NodeJS.Timeout;
  return ((...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  }) as T;
}

export default function StockListScreen() {
  const {
    stocks,
    watchlist,
    isRefreshing,
    refreshAllData,
    addToWatchlist,
    removeFromWatchlist,
  } = useAppStore();
  const { theme } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState('');
  const searchInputRef = useRef<TextInput>(null);

  // Create debounced version of setDebouncedQuery
  const debouncedSetQuery = useRef(
    debounce((text: string) => setDebouncedQuery(text), 300)
  ).current;

  // Update debounced query when searchQuery changes
  useEffect(() => {
    debouncedSetQuery(searchQuery);
    // Clean up on unmount
    return () => {
      debouncedSetQuery.cancel?.();
    };
  }, [searchQuery, debouncedSetQuery]);

  const handleSearchChange = useCallback((text: string) => {
    setSearchQuery(text); // Update immediately for responsive UI
  }, []);

  const handleClearSearch = useCallback(() => {
    setSearchQuery('');
    // Focus back on the input after clearing
    setTimeout(() => {
      searchInputRef.current?.focus();
    }, 100);
  }, []);

  const filteredStocks = useMemo(() => {
    return searchStocks(stocks, debouncedQuery);
  }, [stocks, debouncedQuery]);

  const watchlistTickers = useMemo(() => {
    return new Set(watchlist.map(item => item.ticker));
  }, [watchlist]);

  const handleStockPress = useCallback((stock: ProcessedStock) => {
    router.push(`/stock-details/${stock.ticker}`);
  }, []);

  const handleWatchlistToggle = useCallback(async (ticker: string) => {
    try {
      if (watchlistTickers.has(ticker)) {
        await removeFromWatchlist(ticker);
      } else {
        await addToWatchlist(ticker);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to update watchlist');
    }
  }, [watchlistTickers, removeFromWatchlist, addToWatchlist]);

  const handlePriceAlert = useCallback((ticker: string) => {
    router.push(`/price-alert/${ticker}`);
  }, []);

  const renderStockItem = useCallback(({ item: stock }: { item: ProcessedStock }) => {
    const isInWatchlist = watchlistTickers.has(stock.ticker);
    return (
      <TouchableOpacity
        style={[styles.stockCard, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}
        onPress={() => handleStockPress(stock)}
      >
        <View style={styles.stockHeader}>
          <View style={styles.stockInfo}>
            <Text style={[styles.stockTicker, { color: theme.colors.text }]}>{stock.ticker}</Text>
            <Text style={[styles.stockName, { color: theme.colors.textSecondary }]} numberOfLines={2}>
              {stock.ticker_name}
            </Text>
          </View>
          <View style={styles.stockActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleWatchlistToggle(stock.ticker)}
            >
              <Ionicons
                name={isInWatchlist ? "star" : "star-outline"}
                size={20}
                color={isInWatchlist ? theme.colors.warning : theme.colors.textSecondary}
              />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handlePriceAlert(stock.ticker)}
            >
              <Ionicons name="notifications-outline" size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          </View>
        </View>
        <View style={styles.stockPricing}>
          <View style={styles.priceSection}>
            <Text style={[styles.currentPrice, { color: theme.colors.text }]}>{stock.formattedLTP}</Text>
            <Text style={[styles.priceChange, { color: getStockChangeColor(stock.point_change, theme) }]}>
              {stock.formattedChange}
            </Text>
          </View>
        </View>
        <View style={styles.stockDetails}>
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Open</Text>
            <Text style={styles.detailValue}>₹{stock.open.toFixed(2)}</Text>
          </View>
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>High</Text>
            <Text style={styles.detailValue}>₹{stock.high.toFixed(2)}</Text>
          </View>
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Low</Text>
            <Text style={styles.detailValue}>₹{stock.low.toFixed(2)}</Text>
          </View>
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Volume</Text>
            <Text style={styles.detailValue}>{stock.formattedVolume}</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  }, [watchlistTickers, theme, handleStockPress, handleWatchlistToggle, handlePriceAlert]);

  const renderEmptyState = useCallback(() => (
    <View style={styles.emptyState}>
      <Ionicons name="search-outline" size={64} color={theme.colors.textSecondary} />
      <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>No stocks found</Text>
      <Text style={[styles.emptySubtitle, { color: theme.colors.textSecondary }]}>
        Try adjusting your search terms or check back later
      </Text>
    </View>
  ), [theme]);

  const keyExtractor = useCallback((item: ProcessedStock) => item.ticker, []);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Fixed Header - Outside of FlatList */}
      <View style={styles.header}>
        <View style={[styles.searchContainer, { backgroundColor: theme.colors.surface }]}>
          <Ionicons name="search" size={20} color={theme.colors.textSecondary} style={styles.searchIcon} />
          <TextInput
            ref={searchInputRef}
            style={[styles.searchInput, { color: theme.colors.text }]}
            placeholder="Search stocks by symbol or name..."
            value={searchQuery}
            onChangeText={handleSearchChange}
            placeholderTextColor={theme.colors.textSecondary}
            autoCorrect={false}
            autoCapitalize="none"
            clearButtonMode="never"
            returnKeyType="search"
            blurOnSubmit={false}
            selectTextOnFocus={false}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={handleClearSearch} style={styles.clearButton}>
              <Ionicons name="close-circle" size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>
        <View style={styles.statsContainer}>
          <Text style={[styles.statsText, { color: theme.colors.textSecondary }]}>
            {searchQuery ? `${filteredStocks.length} of ${stocks.length}` : `${stocks.length}`} stocks
          </Text>
        </View>
      </View>
      {/* FlatList for stocks */}
      <FlatList
        data={filteredStocks}
        renderItem={renderStockItem}
        keyExtractor={keyExtractor}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={refreshAllData}
            colors={[theme.colors.primary]}
          />
        }
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        keyboardDismissMode="none"
        removeClippedSubviews={false}
        maxToRenderPerBatch={10}
        updateCellsBatchingPeriod={50}
        initialNumToRender={10}
        windowSize={10}
        getItemLayout={undefined} // Disable for dynamic heights
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  listContainer: {
    padding: 16,
    paddingTop: 0, // Remove top padding since header has padding
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#000000',
    paddingVertical: 4,
  },
  clearButton: {
    marginLeft: 8,
  },
  statsContainer: {
    alignItems: 'center',
  },
  statsText: {
    fontSize: 14,
    color: '#8E8E93',
  },
  stockCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  stockHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  stockInfo: {
    flex: 1,
    marginRight: 12,
  },
  stockTicker: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  stockName: {
    fontSize: 14,
    color: '#8E8E93',
    lineHeight: 18,
  },
  stockActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#F2F2F7',
  },
  stockPricing: {
    marginBottom: 12,
  },
  priceSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  currentPrice: {
    fontSize: 20,
    fontWeight: '600',
    color: '#000000',
  },
  priceChange: {
    fontSize: 16,
    fontWeight: '500',
  },
  stockDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#F2F2F7',
  },
  detailItem: {
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: 12,
    color: '#8E8E93',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#000000',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
    paddingHorizontal: 32,
  },
});