import { useTheme } from '@/contexts/ThemeContext';
import { View } from 'react-native';

// Themed background for web and Android
export default function TabBarBackground() {
  const { theme } = useTheme();

  return (
    <View
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: theme.colors.surface,
      }}
    />
  );
}

export function useBottomTabOverflow() {
  return 0;
}
