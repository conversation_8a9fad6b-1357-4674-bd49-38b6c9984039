# IPO Alert - Stock Market App 📈

A comprehensive React Native mobile app built with Expo that combines IPO notifications, stock market functionality, and portfolio tracking. Get notified about IPOs, track stock performance, and manage your investment portfolio all in one place.

## 🚀 Features

### IPO Management
- **Real-time IPO Tracking**: View ongoing and upcoming IPOs
- **Smart Notifications**: Receive daily reminders (9 AM & 8 PM) about open IPOs
- **Application Status**: Mark IPOs as applied and track your applications
- **Detailed IPO Information**: Company details, offer price, shares offered, and more

### Stock Market
- **Live Stock Data**: Real-time stock prices and market data
- **Advanced Search**: Search stocks by ticker or company name
- **Top Performers**: View top gainers and losers
- **Watchlist**: Add stocks to your personal watchlist
- **Price Alerts**: Set custom price alerts with background monitoring

### Portfolio Management
- **SQLite Database**: Secure local storage for your holdings
- **Portfolio Analytics**: Track profit/loss, performance metrics
- **Holdings Management**: Add, edit, and delete stock holdings
- **Real-time Valuation**: Current portfolio value based on live prices

### Smart Notifications
- **IPO Reminders**: Automated daily notifications for open IPOs
- **Price Alerts**: Get notified when stocks hit your target prices
- **Background Processing**: Alerts work even when app is closed
- **Customizable Settings**: Control notification preferences

## 🛠 Tech Stack

- **Framework**: Expo SDK (latest) with Expo Router
- **Navigation**: File-based routing with tab navigation
- **State Management**: Zustand for global state
- **Database**: expo-sqlite for portfolio data
- **Storage**: AsyncStorage for app data
- **Notifications**: Expo Notifications with background tasks
- **UI**: React Native with custom components
- **Date Handling**: Moment.js
- **Icons**: Expo Vector Icons

## 🔧 Installation & Setup

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Start the Development Server**
   ```bash
   npm start
   ```

3. **Run on Device/Emulator**
   - Scan QR code with Expo Go app (iOS/Android)
   - Press `a` for Android emulator
   - Press `i` for iOS simulator

## 🧪 Testing

Run the built-in test script to verify app structure:

```bash
node scripts/test-app.js
```

## 📱 App Features Implemented

✅ IPO tracking with notifications
✅ Stock market data with search
✅ Portfolio management with SQLite
✅ Watchlist functionality
✅ Price alerts with background tasks
✅ Settings and preferences
✅ Tab-based navigation
✅ Modal screens for detailed views

**Made with ❤️ for investors and traders**
