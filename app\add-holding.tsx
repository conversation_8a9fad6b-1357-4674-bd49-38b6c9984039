import { useTheme } from '@/contexts/ThemeContext';
import { useAppStore } from '@/store/appStore';
import { validatePrice, validateQuantity } from '@/utils/dataProcessing';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { router } from 'expo-router';
import React, { useState } from 'react';
import { Alert, KeyboardAvoidingView, Platform, ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function AddHoldingScreen() {
  const { addPortfolioHolding, stocks } = useAppStore();
  const { theme } = useTheme();

  const [ticker, setTicker] = useState('');
  const [quantity, setQuantity] = useState('');
  const [averagePrice, setAveragePrice] = useState('');
  const [purchaseDate, setPurchaseDate] = useState(new Date());
  const [purchaseDateString, setPurchaseDateString] = useState(new Date().toISOString().split('T')[0]);
  const [isLoading, setIsLoading] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);

  // Dropdown search states
  const [searchQuery, setSearchQuery] = useState('');
  const [showDropdown, setShowDropdown] = useState(false);
  const [selectedStock, setSelectedStock] = useState<any>(null);

  // Filter stocks based on search query
  const filteredStocks = stocks.filter(stock =>
    stock.ticker.toLowerCase().includes(searchQuery.toLowerCase()) ||
    stock.ticker_name.toLowerCase().includes(searchQuery.toLowerCase())
  ).slice(0, 10); // Limit to 10 results for performance

  const handleStockSelect = (stock: any) => {
    setSelectedStock(stock);
    setTicker(stock.ticker);
    setSearchQuery(stock.ticker);
    setShowDropdown(false);
  };

  const handleSearchChange = (text: string) => {
    setSearchQuery(text);
    setTicker(text);
    setShowDropdown(text.length > 0);

    // Clear selected stock if user types something different
    if (selectedStock && text !== selectedStock.ticker) {
      setSelectedStock(null);
    }
  };

  const onDateChange = (_event: any, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      setShowDatePicker(false);
    }

    if (selectedDate) {
      setPurchaseDate(selectedDate);
      setPurchaseDateString(selectedDate.toISOString().split('T')[0]);

      if (Platform.OS === 'ios') {
        setShowDatePicker(false);
      }
    }
  };

  const handleCloseDropdown = () => {
    setShowDropdown(false);
  };

  const handleSave = async () => {
    // Validation
    if (!selectedStock) {
      Alert.alert('Error', 'Please select a stock from the dropdown');
      return;
    }

    if (!ticker.trim()) {
      Alert.alert('Error', 'Please select a stock ticker');
      return;
    }

    const quantityNum = parseFloat(quantity);
    if (!quantity || isNaN(quantityNum) || !validateQuantity(quantityNum)) {
      Alert.alert('Error', 'Please enter a valid quantity (1-1,000,000)');
      return;
    }

    const priceNum = parseFloat(averagePrice);
    if (!averagePrice || isNaN(priceNum) || !validatePrice(priceNum)) {
      Alert.alert('Error', 'Please enter a valid price (₹0.01-₹10,00,000)');
      return;
    }

    if (!purchaseDateString) {
      Alert.alert('Error', 'Please enter a purchase date');
      return;
    }

    // Check if stock exists in our data
    const stockExists = stocks.some(stock => stock.ticker.toUpperCase() === ticker.trim().toUpperCase());
    if (!stockExists) {
      Alert.alert(
        'Stock Not Found',
        `${ticker.toUpperCase()} is not found in our database. Do you want to add it anyway?`,
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Add Anyway', onPress: () => saveHolding() },
        ]
      );
      return;
    }

    saveHolding();
  };

  const saveHolding = async () => {
    setIsLoading(true);
    try {
      await addPortfolioHolding({
        ticker: ticker.trim().toUpperCase(),
        quantity: parseFloat(quantity),
        averagePrice: parseFloat(averagePrice),
        purchaseDate: purchaseDateString,
      });

      Alert.alert('Success', 'Holding added successfully', [
        { text: 'OK', onPress: () => router.back() },
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to add holding. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  const calculateTotalValue = () => {
    const quantityNum = parseFloat(quantity) || 0;
    const priceNum = parseFloat(averagePrice) || 0;
    return quantityNum * priceNum;
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={[styles.header, { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border }]}>
          <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
            <Text style={[styles.cancelText, { color: theme.colors.textSecondary }]}>Cancel</Text>
          </TouchableOpacity>
          <Text style={[styles.title, { color: theme.colors.text }]}>Add Holding</Text>
          <TouchableOpacity
            style={[styles.saveButton, { backgroundColor: theme.colors.primary }, isLoading && styles.disabledButton]}
            onPress={handleSave}
            disabled={isLoading}
          >
            <Text style={[styles.saveText, { color: theme.colors.surface }]}>Save</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <TouchableOpacity style={styles.form} activeOpacity={1} onPress={handleCloseDropdown}>
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: theme.colors.text }]}>Stock Ticker *</Text>
              <View style={styles.searchContainer}>
                <TextInput
                  style={[styles.input, { backgroundColor: theme.colors.surface, color: theme.colors.text, borderColor: theme.colors.border }]}
                  value={searchQuery}
                  onChangeText={handleSearchChange}
                  placeholder="Search stocks (e.g., AAPL, Apple Inc.)"
                  placeholderTextColor={theme.colors.textSecondary}
                  autoCapitalize="characters"
                  autoCorrect={false}
                  onFocus={() => setShowDropdown(searchQuery.length > 0)}
                />
                <Ionicons
                  name="search"
                  size={20}
                  color={theme.colors.textSecondary}
                  style={styles.searchIcon}
                />
              </View>

              {showDropdown && filteredStocks.length > 0 && (
                <View style={[styles.dropdown, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}>
                  <ScrollView style={styles.dropdownScroll} nestedScrollEnabled>
                    {filteredStocks.map((stock, index) => (
                      <TouchableOpacity
                        key={index}
                        style={[styles.dropdownItem, { borderBottomColor: theme.colors.border }]}
                        onPress={() => handleStockSelect(stock)}
                      >
                        <View style={styles.stockItem}>
                          <Text style={[styles.stockTicker, { color: theme.colors.text }]}>{stock.ticker}</Text>
                          <Text style={[styles.stockName, { color: theme.colors.textSecondary }]} numberOfLines={1}>
                            {stock.ticker_name}
                          </Text>
                          <Text style={[styles.stockPrice, { color: theme.colors.text }]}>
                            ₹{stock.ltp.toFixed(2)}
                          </Text>
                        </View>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                </View>
              )}

              {selectedStock && (
                <View style={[styles.selectedStockCard, { backgroundColor: theme.colors.primary + '10', borderColor: theme.colors.primary }]}>
                  <View style={styles.selectedStockInfo}>
                    <Text style={[styles.selectedStockTicker, { color: theme.colors.primary }]}>{selectedStock.ticker}</Text>
                    <Text style={[styles.selectedStockName, { color: theme.colors.text }]}>{selectedStock.ticker_name}</Text>
                    <Text style={[styles.selectedStockPrice, { color: theme.colors.textSecondary }]}>
                      Current Price: ₹{selectedStock.ltp.toFixed(2)}
                    </Text>
                  </View>
                  <Ionicons name="checkmark-circle" size={24} color={theme.colors.primary} />
                </View>
              )}

              <Text style={[styles.hint, { color: theme.colors.textSecondary }]}>
                Search and select a stock from the available options
              </Text>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Quantity *</Text>
              <TextInput
                style={styles.input}
                value={quantity}
                onChangeText={setQuantity}
                placeholder="Number of shares"
                keyboardType="numeric"
              />
              <Text style={styles.hint}>Number of shares you own</Text>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Average Price (₹) *</Text>
              <TextInput
                style={styles.input}
                value={averagePrice}
                onChangeText={setAveragePrice}
                placeholder="Price per share"
                keyboardType="decimal-pad"
              />
              <Text style={styles.hint}>Average price paid per share</Text>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Purchase Date *</Text>
              <TouchableOpacity
                style={styles.input}
                onPress={() => setShowDatePicker(true)}
              >
                <Text style={styles.dateText}>
                  {purchaseDateString || 'Select purchase date'}
                </Text>
                <Ionicons name="calendar-outline" size={20} color="#8E8E93" />
              </TouchableOpacity>
              <Text style={styles.hint}>Date when you purchased the stock</Text>
            </View>

            {quantity && averagePrice && (
              <View style={styles.summaryCard}>
                <Text style={styles.summaryTitle}>Investment Summary</Text>
                <View style={styles.summaryRow}>
                  <Text style={styles.summaryLabel}>Total Shares</Text>
                  <Text style={styles.summaryValue}>{quantity}</Text>
                </View>
                <View style={styles.summaryRow}>
                  <Text style={styles.summaryLabel}>Average Price</Text>
                  <Text style={styles.summaryValue}>₹{parseFloat(averagePrice).toFixed(2)}</Text>
                </View>
                <View style={styles.summaryRow}>
                  <Text style={styles.summaryLabel}>Total Investment</Text>
                  <Text style={[styles.summaryValue, styles.totalValue]}>
                    ₹{calculateTotalValue().toFixed(2)}
                  </Text>
                </View>
              </View>
            )}
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>

      {showDatePicker && (
        <DateTimePicker
          value={purchaseDate}
          mode="date"
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={onDateChange}
          maximumDate={new Date()}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  keyboardView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  cancelButton: {
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  cancelText: {
    fontSize: 16,
    color: '#007AFF',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  saveButton: {
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  disabledButton: {
    opacity: 0.5,
  },
  saveText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  form: {
    padding: 16,
  },
  inputGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#000000',
    borderWidth: 1,
    borderColor: '#E5E5EA',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 16,
    color: '#000000',
  },
  hint: {
    fontSize: 14,
    color: '#8E8E93',
    marginTop: 4,
  },
  summaryCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginTop: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 16,
    color: '#8E8E93',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000000',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: '600',
    color: '#007AFF',
  },
  // Search dropdown styles
  searchContainer: {
    position: 'relative',
  },
  searchIcon: {
    position: 'absolute',
    right: 16,
    top: 12,
  },
  dropdown: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    maxHeight: 200,
    borderRadius: 8,
    borderWidth: 1,
    marginTop: 4,
    zIndex: 1000,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  dropdownScroll: {
    maxHeight: 200,
  },
  dropdownItem: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  stockItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  stockTicker: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  stockName: {
    fontSize: 14,
    flex: 2,
    marginHorizontal: 8,
  },
  stockPrice: {
    fontSize: 14,
    fontWeight: '500',
  },
  // Selected stock card
  selectedStockCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginTop: 8,
  },
  selectedStockInfo: {
    flex: 1,
  },
  selectedStockTicker: {
    fontSize: 16,
    fontWeight: '600',
  },
  selectedStockName: {
    fontSize: 14,
    marginTop: 2,
  },
  selectedStockPrice: {
    fontSize: 12,
    marginTop: 2,
  },
});
