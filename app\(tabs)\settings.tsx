import { Theme, ThemeMode, useTheme } from '@/contexts/ThemeContext';
import { useAppStore } from '@/store/appStore';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import React, { useState } from 'react';
import { Alert, Platform, ScrollView, StyleSheet, Switch, Text, TouchableOpacity, View,Linking } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function SettingsScreen() {
  const { settings, updateSettings } = useAppStore();
  const { theme, themeMode, setThemeMode } = useTheme();
  const [localSettings, setLocalSettings] = useState(settings);
  const [showTimePicker, setShowTimePicker] = useState<'time1' | 'time2' | null>(null);

  const handleToggle = async (key: keyof typeof settings, value: boolean) => {
    const newSettings = { ...localSettings, [key]: value };
    setLocalSettings(newSettings);
    await updateSettings({ [key]: value });
  };

  const handleTimeChange = (timeKey: 'ipoNotificationTime1' | 'ipoNotificationTime2') => {
    setShowTimePicker(timeKey === 'ipoNotificationTime1' ? 'time1' : 'time2');
  };

  const onTimePickerChange = async (event: any, selectedTime?: Date) => {
    if (Platform.OS === 'android') {
      setShowTimePicker(null);
    }

    if (selectedTime && showTimePicker) {
      const timeString = selectedTime.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });

      const timeKey = showTimePicker === 'time1' ? 'ipoNotificationTime1' : 'ipoNotificationTime2';
      const newSettings = { ...localSettings, [timeKey]: timeString };
      setLocalSettings(newSettings);
      await updateSettings({ [timeKey]: timeString });

      if (Platform.OS === 'ios') {
        setShowTimePicker(null);
      }
    }
  };

  const handleThemeChange = (mode: ThemeMode) => {
    setThemeMode(mode);
  };

  const handleClearData = () => {
    Alert.alert(
      'Clear All Data',
      'This will remove all your portfolio holdings, watchlist, and alerts. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear Data',
          style: 'destructive',
          onPress: () => {
            // Implementation would go here
            Alert.alert('Success', 'All data has been cleared');
          }
        },
      ]
    );
  };

  const dynamicStyles = createDynamicStyles(theme);

  return (
    <SafeAreaView style={dynamicStyles.container}>
      <View style={dynamicStyles.header}>
        <Text style={dynamicStyles.title}>Settings</Text>
      </View>

      <ScrollView style={dynamicStyles.content}>
        {/* Appearance Section */}
        <View style={dynamicStyles.section}>
          <Text style={dynamicStyles.sectionTitle}>Appearance</Text>

          <View style={dynamicStyles.themeSelector}>
            <Text style={dynamicStyles.settingLabel}>Theme</Text>
            <View style={dynamicStyles.themeOptions}>
              {(['light', 'dark', 'system'] as ThemeMode[]).map((mode) => (
                <TouchableOpacity
                  key={mode}
                  style={[
                    dynamicStyles.themeOption,
                    themeMode === mode && dynamicStyles.selectedThemeOption,
                  ]}
                  onPress={() => handleThemeChange(mode)}
                >
                  <Ionicons
                    name={
                      mode === 'light' ? 'sunny' :
                        mode === 'dark' ? 'moon' : 'phone-portrait'
                    }
                    size={20}
                    color={themeMode === mode ? theme.colors.surface : theme.colors.text}
                  />
                  <Text style={[
                    dynamicStyles.themeOptionText,
                    themeMode === mode && dynamicStyles.selectedThemeOptionText
                  ]}>
                    {mode.charAt(0).toUpperCase() + mode.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>

        {/* Notifications Section */}
        <View style={dynamicStyles.section}>
          <Text style={dynamicStyles.sectionTitle}>Notifications</Text>

          <View style={dynamicStyles.settingItem}>
            <View style={dynamicStyles.settingInfo}>
              <Text style={dynamicStyles.settingLabel}>Enable Notifications</Text>
              <Text style={dynamicStyles.settingDescription}>
                Receive IPO reminders and price alerts
              </Text>
            </View>
            <Switch
              value={localSettings.notificationsEnabled}
              onValueChange={(value) => handleToggle('notificationsEnabled', value)}
              trackColor={{ false: theme.colors.border, true: theme.colors.success }}
              thumbColor={theme.colors.surface}
            />
          </View>

          <TouchableOpacity
            style={dynamicStyles.settingItem}
            onPress={() => handleTimeChange('ipoNotificationTime1')}
            disabled={!localSettings.notificationsEnabled}
          >
            <View style={dynamicStyles.settingInfo}>
              <Text style={[dynamicStyles.settingLabel, !localSettings.notificationsEnabled && dynamicStyles.disabled]}>
                Morning Reminder
              </Text>
              <Text style={[dynamicStyles.settingDescription, !localSettings.notificationsEnabled && dynamicStyles.disabled]}>
                {localSettings.ipoNotificationTime1}
              </Text>
            </View>
            <Ionicons
              name="chevron-forward"
              size={20}
              color={localSettings.notificationsEnabled ? theme.colors.textSecondary : theme.colors.textSecondary}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={dynamicStyles.settingItem}
            onPress={() => handleTimeChange('ipoNotificationTime2')}
            disabled={!localSettings.notificationsEnabled}
          >
            <View style={dynamicStyles.settingInfo}>
              <Text style={[dynamicStyles.settingLabel, !localSettings.notificationsEnabled && dynamicStyles.disabled]}>
                Evening Reminder
              </Text>
              <Text style={[dynamicStyles.settingDescription, !localSettings.notificationsEnabled && dynamicStyles.disabled]}>
                {localSettings.ipoNotificationTime2}
              </Text>
            </View>
            <Ionicons
              name="chevron-forward"
              size={20}
              color={localSettings.notificationsEnabled ? theme.colors.textSecondary : theme.colors.textSecondary}
            />
          </TouchableOpacity>

          <View style={dynamicStyles.settingItem}>
            <View style={dynamicStyles.settingInfo}>
              <Text style={dynamicStyles.settingLabel}>Price Alert Notifications</Text>
              <Text style={dynamicStyles.settingDescription}>
                Get notified when stocks reach your target prices
              </Text>
            </View>
            <Switch
              value={localSettings.priceAlertNotifications}
              onValueChange={(value) => handleToggle('priceAlertNotifications', value)}
              trackColor={{ false: theme.colors.border, true: theme.colors.success }}
              thumbColor={theme.colors.surface}
            />
          </View>
        </View>

        {/* Data & Privacy Section */}
        <View style={dynamicStyles.section}>
          <Text style={dynamicStyles.sectionTitle}>Data & Privacy</Text>

          <View style={dynamicStyles.settingItem}>
            <View style={dynamicStyles.settingInfo}>
              <Text style={dynamicStyles.settingLabel}>Background Refresh</Text>
              <Text style={dynamicStyles.settingDescription}>
                Allow app to refresh data in background
              </Text>
            </View>
            <Switch
              value={localSettings.backgroundRefreshEnabled}
              onValueChange={(value) => handleToggle('backgroundRefreshEnabled', value)}
              trackColor={{ false: theme.colors.border, true: theme.colors.success }}
              thumbColor={theme.colors.surface}
            />
          </View>

          <TouchableOpacity style={dynamicStyles.settingItem} onPress={handleClearData}>
            <View style={dynamicStyles.settingInfo}>
              <Text style={[dynamicStyles.settingLabel, dynamicStyles.destructive]}>Clear All Data</Text>
              <Text style={dynamicStyles.settingDescription}>
                Remove all portfolio, watchlist, and alert data
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={theme.colors.error} />
          </TouchableOpacity>
        </View>

        {/* About Section */}
        <View style={dynamicStyles.section}>
          <Text style={dynamicStyles.sectionTitle}>About</Text>

          <View style={dynamicStyles.settingItem}>
            <View style={dynamicStyles.settingInfo}>
              <Text style={dynamicStyles.settingLabel}>App Version</Text>
              <Text style={dynamicStyles.settingDescription}>1.0.0</Text>
            </View>
          </View>

          <TouchableOpacity style={dynamicStyles.settingItem}>
            <View style={dynamicStyles.settingInfo}>
              <Text style={dynamicStyles.settingLabel}>Privacy Policy</Text>
              <Text style={dynamicStyles.settingDescription}>
                Learn how we protect your data
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
          </TouchableOpacity>

          <TouchableOpacity style={dynamicStyles.settingItem}>
            <View style={dynamicStyles.settingInfo}>
              <Text style={dynamicStyles.settingLabel}>Terms of Service</Text>
              <Text style={dynamicStyles.settingDescription}>
                Read our terms and conditions
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        </View>

        {/* Footer */}
        <View style={dynamicStyles.footer}>
          <Text style={dynamicStyles.footerText}>IPO Alert - Stock Market App</Text>
          <Text style={dynamicStyles.footerSubtext}>
            Made with ❤️ for investors by{' '}
            <Text
              style={[dynamicStyles.footerSubtext, { color: '#007AFF', textDecorationLine: 'underline' }]}
              onPress={() => Linking.openURL('https://ashishkamat.com.np')}
            >
              Ashish Kamat
            </Text>
          </Text>
        </View>
      </ScrollView>

      {showTimePicker && (
        <DateTimePicker
          value={new Date()}
          mode="time"
          is24Hour={false}
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={onTimePickerChange}
        />
      )}
    </SafeAreaView>
  );
}

const createDynamicStyles = (theme: Theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  content: {
    flex: 1,
  },
  themeSelector: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  themeOptions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 8,
  },
  themeOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
    backgroundColor: theme.colors.surface,
    gap: 8,
  },
  selectedThemeOption: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  themeOptionText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
  },
  selectedThemeOptionText: {
    color: theme.colors.surface,
  },
  section: {
    marginTop: 24,
    backgroundColor: theme.colors.surface,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: theme.colors.border,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.textSecondary,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.background,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  settingInfo: {
    flex: 1,
    marginRight: 12,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 18,
  },
  disabled: {
    color: theme.colors.textSecondary,
    opacity: 0.5,
  },
  destructive: {
    color: theme.colors.error,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 16,
  },
  footerText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  footerSubtext: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
});
