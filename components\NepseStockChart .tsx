import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Dimensions, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { LineChartBicolor } from "react-native-gifted-charts";
import { useTheme } from '@/contexts/ThemeContext';

// Type definitions for the API response
interface ChartDataItem {
    value: number;
    timestamp: string;
    volume: number;
}
interface IndexData {
    indices_name: string;
    point_change: number;
    percentage_change: number;
    calculated_on: string;
    latest_price: number;
    chartData: ChartDataItem[];
}
interface ApiResponse {
    success: boolean;
    type: string;
    timeRange?: string;
    data: IndexData;
}
type TimeRange = "1d" | "1m" | "3m" | "1y" | "5y" | "all";
type TimeRangeData = {
    [key in TimeRange]: IndexData | null;
};

const NepseStockChart = () => {
    const { theme } = useTheme();
    const [data, setData] = useState<IndexData | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [containerWidth, setContainerWidth] = useState(Dimensions.get('window').width - 32);
    const [timeRange, setTimeRange] = useState<TimeRange>("1d");
    const [allTimeRangeData, setAllTimeRangeData] = useState<TimeRangeData>({
        "1d": null,
        "1m": null,
        "3m": null,
        "1y": null,
        "5y": null,
        "all": null,
    });
    const [pointerData, setPointerData] = useState<{ value: number, timestamp: string } | null>(null);

    useEffect(() => {
        const fetchAllData = async () => {
            try {
                setLoading(true);
                const timeRanges: TimeRange[] = ["1d", "1m", "3m", "1y", "5y", "all"];
                const promises = timeRanges.map(range =>
                    fetch(`https://nepse-data-y5f1.onrender.com/api/indices/${range}`)
                        .then(response => response.json())
                );
                const responses = await Promise.all(promises);
                const newData: TimeRangeData = {
                    "1d": null,
                    "1m": null,
                    "3m": null,
                    "1y": null,
                    "5y": null,
                    "all": null,
                };
                let hasValidData = false;
                responses.forEach((response: ApiResponse, index) => {
                    if (response.success && response.data) {
                        newData[timeRanges[index]] = response.data;
                        hasValidData = true;
                    }
                });
                if (hasValidData) {
                    setAllTimeRangeData(newData);
                    // Set initial data to the default time range
                    setData(newData["1d"]);
                } else {
                    setError("Invalid API response for all time ranges");
                }
            } catch (err) {
                setError("Failed to fetch data");
                console.error(err);
            } finally {
                setLoading(false);
            }
        };
        fetchAllData();
    }, []);

    // Update container width on dimension change
    useEffect(() => {
        const handleDimensionsChange = () => {
            setContainerWidth(Dimensions.get('window').width - 32);
        };
        const subscription = Dimensions.addEventListener('change', handleDimensionsChange);
        return () => subscription?.remove();
    }, []);

    const formatNumber = (num: number): string => {
        return num.toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    };

    const formatPercentage = (num: number): string => {
        return `${num >= 0 ? '+' : ''}${num.toFixed(2)}%`;
    };

    const getPriceChangeColor = (change: number): string => {
        return change >= 0 ? theme.colors.success : theme.colors.error;
    };

    // Format timestamp for x-axis labels
    const formatTimestamp = (timestamp: string, range: TimeRange): string => {
        const date = new Date(timestamp);
        switch (range) {
            case '1d':
                return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            case '1m':
            case '3m':
                return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
            case '1y':
            case '5y':
            case 'all':
                return date.toLocaleDateString([], { year: '2-digit', month: 'short' });
            default:
                return date.toLocaleDateString();
        }
    };

    // Transform data for LineChartBicolor
    const getChartData = () => {
        if (!data || !data.chartData) return [];
        // Limit to 20 data points for better performance
        const step = Math.ceil(data.chartData.length / 20);
        return data.chartData
            .filter((_, index) => index % step === 0)
            .map((item, index) => ({
                value: item.value,
                dataPointText: formatNumber(item.value),
                labelText: index % 4 === 0 ? formatTimestamp(item.timestamp, timeRange) : '', // Show every 4th label
                timestamp: item.timestamp,
            }));
    };

    // Get min and max values for chart
    const getChartRange = () => {
        if (!data || !data.chartData) return { minValue: 0, maxValue: 0 };
        const values = data.chartData.map(item => item.value);
        const minValue = Math.min(...values);
        const maxValue = Math.max(...values);
        // Add some padding to the range
        const range = maxValue - minValue;
        const padding = range * 0.1;
        return {
            minValue: minValue - padding,
            maxValue: maxValue + padding,
        };
    };

    const handleTimeRangeChange = (range: TimeRange) => {
        if (range !== timeRange && allTimeRangeData[range]) {
            setTimeRange(range);
            setData(allTimeRangeData[range]);
            setPointerData(null); // Clear pointer data when changing time range
        }
    };

    // Handle pointer events
    const handlePointerMove = (items: any[]) => {
        if (items && items.length > 0) {
            const item = items[0];
            setPointerData({
                value: item.value,
                timestamp: item.timestamp || new Date().toISOString()
            });
        }
    };

    const handlePointerEnd = () => {
        setPointerData(null);
    };

    if (loading) {
        return (
            <View style={[styles.container, { backgroundColor: theme.colors.surface }]}>
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={theme.colors.primary} />
                    <Text style={[styles.loadingText, { color: theme.colors.textSecondary }]}>Loading chart data...</Text>
                </View>
            </View>
        );
    }

    if (error) {
        return (
            <View style={[styles.container, { backgroundColor: theme.colors.surface }]}>
                <View style={styles.errorContainer}>
                    <Text style={[styles.errorText, { color: theme.colors.error }]}>{error}</Text>
                </View>
            </View>
        );
    }

    if (!data) {
        return (
            <View style={[styles.container, { backgroundColor: theme.colors.surface }]}>
                <View style={styles.errorContainer}>
                    <Text style={[styles.errorText, { color: theme.colors.textSecondary }]}>No data available</Text>
                </View>
            </View>
        );
    }

    const chartData = getChartData();
    const { minValue, maxValue } = getChartRange();
    const chartWidth = containerWidth - 32; // Account for container padding

    return (
        <View style={[styles.container, { backgroundColor: theme.colors.surface }]} onLayout={(event) => {
            const { width } = event.nativeEvent.layout;
            setContainerWidth(width);
        }}>
            <Text style={[styles.title, { color: theme.colors.text }]}>{data.indices_name} Index Chart</Text>

            {/* Time range selector */}
            <View style={styles.timeRangeContainer}>
                {(["1d", "1m", "3m", "1y", "5y", "all"] as const).map((range) => (
                    <TouchableOpacity
                        key={range}
                        style={[
                            styles.timeRangeButton,
                            timeRange === range && { backgroundColor: theme.colors.primary },
                            !allTimeRangeData[range] && styles.disabledTimeRangeButton
                        ]}
                        onPress={() => handleTimeRangeChange(range)}
                        disabled={!allTimeRangeData[range]}
                    >
                        <Text style={[
                            styles.timeRangeText,
                            timeRange === range && { color: theme.colors.surface },
                            !allTimeRangeData[range] && { color: theme.colors.textDisabled }
                        ]}>
                            {range.toUpperCase()}
                        </Text>
                    </TouchableOpacity>
                ))}
            </View>

            {/* Current price display */}
            <View style={styles.priceContainer}>
                <Text style={[styles.currentPrice, { color: theme.colors.text }]}>
                    {pointerData ? formatNumber(pointerData.value) : formatNumber(data.latest_price)}
                </Text>
                <View style={styles.changeContainer}>
                    <Text style={[
                        styles.priceChange,
                        { color: getPriceChangeColor(data.point_change) }
                    ]}>
                        {data.point_change >= 0 ? '+' : ''}{formatNumber(data.point_change)}
                    </Text>
                    <Text style={[
                        styles.percentageChange,
                        { color: getPriceChangeColor(data.percentage_change) }
                    ]}>
                        ({formatPercentage(data.percentage_change)})
                    </Text>
                </View>
                <Text style={[styles.timestamp, { color: theme.colors.textSecondary }]}>
                    {pointerData
                        ? `Selected: ${new Date(pointerData.timestamp).toLocaleString()}`
                        : `Last Updated: ${new Date(data.calculated_on).toLocaleString()}`
                    }
                </Text>
            </View>

            {/* Chart area */}
            <View style={styles.chartContainer}>
                {chartData.length > 0 ? (
                    <LineChartBicolor
                        data={chartData}
                        height={200}
                        width={chartWidth}
                        areaChart
                        color={theme.colors.success}
                        colorNegative={theme.colors.error}
                        startFillColor={`${theme.colors.success}66`} // Add transparency
                        startFillColorNegative={`${theme.colors.error}66`} // Add transparency
                        endFillColor={`${theme.colors.success}1A`} // Add transparency
                        endFillColorNegative={`${theme.colors.error}1A`} // Add transparency
                        startOpacity={0.8}
                        endOpacity={0.3}
                        curved
                        // Enhanced pointer configuration
                        showPointer
                        pointerConfig={{
                            pointer1Color: theme.colors.primary,
                            pointer2Color: theme.colors.primary,
                            pointerLabelWidth: 100,
                            pointerLabelHeight: 90,
                            activatePointersOnLongPress: true,
                            autoAdjustPointerLabelPosition: true,
                            pointerLabelComponent: (items: any[]) => {
                                if (!items || items.length === 0) return null;
                                const item = items[0];
                                return (
                                    <View style={[styles.pointerLabel, { backgroundColor: `${theme.colors.primary}E6` }]}>
                                        <Text style={[styles.pointerValue, { color: theme.colors.surface }]}>{formatNumber(item.value)}</Text>
                                        <Text style={[styles.pointerTime, { color: theme.colors.surface }]}>
                                            {formatTimestamp(item.timestamp || new Date().toISOString(), timeRange)}
                                        </Text>
                                    </View>
                                );
                            },
                        }}
                        onPointerMove={handlePointerMove}
                        onPointerEnd={handlePointerEnd}
                        // Data points configuration
                        dataPointsColor={theme.colors.success}
                        dataPointsColorNegative={theme.colors.error}
                        dataPointsRadius={4}
                        hideDataPoints={false}
                        // Axis configuration - ENABLED
                        yAxisThickness={1}
                        xAxisThickness={1}
                        yAxisColor={theme.colors.border}
                        xAxisColor={theme.colors.border}
                        // Y-axis styling
                        yAxisTextStyle={{ color: theme.colors.textSecondary, fontSize: 10 }}
                        yAxisLabelWidth={50}
                        noOfSections={4}
                        maxValue={maxValue}
                        minValue={minValue}
                        yAxisLabelTexts={[
                            formatNumber(minValue),
                            formatNumber(minValue + (maxValue - minValue) * 0.25),
                            formatNumber(minValue + (maxValue - minValue) * 0.5),
                            formatNumber(minValue + (maxValue - minValue) * 0.75),
                            formatNumber(maxValue),
                        ]}
                        // X-axis styling
                        xAxisLabelTextStyle={{ color: theme.colors.textSecondary, fontSize: 10 }}
                        rotateLabel
                        xAxisLabelsHeight={40}
                        // Grid lines
                        rulesType="solid"
                        rulesColor={theme.colors.border}
                        showVerticalLines={true}
                        verticalLinesColor={theme.colors.border}
                        // Animation
                        animateOnDataChange
                        animationDuration={300}
                    />
                ) : (
                    <View style={styles.noDataContainer}>
                        <Text style={[styles.noDataText, { color: theme.colors.textSecondary }]}>No chart data available</Text>
                    </View>
                )}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        borderRadius: 12,
        padding: 16,
        marginBottom: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    title: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 12,
        textAlign: 'center',
    },
    timeRangeContainer: {
        flexDirection: 'row',
        justifyContent: 'center',
        marginBottom: 16,
    },
    timeRangeButton: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        marginHorizontal: 4,
        borderRadius: 16,
        backgroundColor: '#f3f4f6',
    },
    disabledTimeRangeButton: {
        opacity: 0.6,
    },
    timeRangeText: {
        fontSize: 12,
        fontWeight: '500',
        color: '#6b7280',
    },
    loadingContainer: {
        height: 200,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingText: {
        marginTop: 8,
        fontSize: 16,
    },
    errorContainer: {
        height: 200,
        justifyContent: 'center',
        alignItems: 'center',
    },
    errorText: {
        fontSize: 16,
        textAlign: 'center',
    },
    priceContainer: {
        alignItems: 'center',
        marginBottom: 16,
    },
    currentPrice: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 4,
    },
    changeContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 4,
    },
    priceChange: {
        fontSize: 16,
        fontWeight: '500',
        marginRight: 8,
    },
    percentageChange: {
        fontSize: 14,
        fontWeight: '500',
    },
    timestamp: {
        fontSize: 12,
    },
    chartContainer: {
        height: 260, // Increased height to accommodate x-axis labels
        marginBottom: 12,
        alignItems: 'center',
        overflow: 'hidden',
    },
    noDataContainer: {
        height: 200,
        justifyContent: 'center',
        alignItems: 'center',
    },
    noDataText: {
        fontSize: 16,
    },
    pointerLabel: {
        padding: 8,
        borderRadius: 6,
        minWidth: 80,
        alignItems: 'center',
    },
    pointerValue: {
        fontSize: 12,
        fontWeight: 'bold',
    },
    pointerTime: {
        fontSize: 10,
        marginTop: 2,
    },
});

export default NepseStockChart;