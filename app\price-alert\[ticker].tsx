import { getStockChangeColor, useTheme } from '@/contexts/ThemeContext';
import { useAppStore } from '@/store/appStore';
import { validatePrice } from '@/utils/dataProcessing';
import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useMemo, useState } from 'react';
import { Alert, ScrollView, StyleSheet, Switch, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function PriceAlertScreen() {
  const { ticker } = useLocalSearchParams<{ ticker: string }>();
  const { stocks, addPriceAlert, priceAlerts } = useAppStore();
  const { theme } = useTheme();

  const [targetPrice, setTargetPrice] = useState('');
  const [condition, setCondition] = useState<'above' | 'below'>('above');
  const [alertType, setAlertType] = useState<'one-time' | 'recurring'>('one-time');
  const [isLoading, setIsLoading] = useState(false);

  const stock = useMemo(() => {
    return stocks.find(s => s.ticker === ticker);
  }, [stocks, ticker]);

  const existingAlerts = useMemo(() => {
    return priceAlerts.filter(alert => alert.ticker === ticker);
  }, [priceAlerts, ticker]);

  const handleSave = async () => {
    if (!targetPrice.trim()) {
      Alert.alert('Error', 'Please enter a target price');
      return;
    }

    const priceNum = parseFloat(targetPrice);
    if (isNaN(priceNum) || !validatePrice(priceNum)) {
      Alert.alert('Error', 'Please enter a valid price (₹0.01-₹10,00,000)');
      return;
    }

    if (!stock) {
      Alert.alert('Error', 'Stock data not available');
      return;
    }

    // Check if similar alert already exists
    const similarAlert = existingAlerts.find(alert => 
      alert.targetPrice === priceNum && 
      alert.condition === condition &&
      !alert.triggered
    );

    if (similarAlert) {
      Alert.alert('Alert Exists', 'A similar price alert already exists for this stock');
      return;
    }

    setIsLoading(true);
    try {
      await addPriceAlert({
        ticker,
        targetPrice: priceNum,
        condition,
        type: alertType,
        triggered: false,
      });

      Alert.alert('Success', 'Price alert created successfully', [
        { text: 'OK', onPress: () => router.back() },
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to create price alert. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  const getAlertDescription = () => {
    if (!targetPrice || !stock) return '';
    
    const currentPrice = stock.ltp;
    const targetPriceNum = parseFloat(targetPrice);
    
    if (isNaN(targetPriceNum)) return '';

    const percentageChange = ((targetPriceNum - currentPrice) / currentPrice) * 100;
    const direction = condition === 'above' ? 'rises' : 'falls';
    
    return `Alert when ${ticker} ${direction} to ₹${targetPriceNum.toFixed(2)} (${percentageChange >= 0 ? '+' : ''}${percentageChange.toFixed(2)}% from current price)`;
  };

  if (!stock) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Stock not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={[styles.header, { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border }]}>
        <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
          <Text style={[styles.cancelText, { color: theme.colors.textSecondary }]}>Cancel</Text>
        </TouchableOpacity>
        <Text style={[styles.title, { color: theme.colors.text }]}>Price Alert</Text>
        <TouchableOpacity
          style={[styles.saveButton, { backgroundColor: theme.colors.primary }, isLoading && styles.disabledButton]}
          onPress={handleSave}
          disabled={isLoading}
        >
          <Text style={[styles.saveText, { color: theme.colors.surface }]}>Save</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={[styles.stockCard, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.stockTicker, { color: theme.colors.text }]}>{stock.ticker}</Text>
          <Text style={[styles.stockName, { color: theme.colors.textSecondary }]}>{stock.ticker_name}</Text>
          <View style={styles.currentPriceSection}>
            <Text style={[styles.currentPriceLabel, { color: theme.colors.textSecondary }]}>Current Price</Text>
            <Text style={[styles.currentPrice, { color: theme.colors.text }]}>₹{stock.ltp.toFixed(2)}</Text>
            <Text style={[styles.priceChange, { color: getStockChangeColor(stock.point_change, theme) }]}>
              {stock.formattedChange}
            </Text>
          </View>
        </View>

        <View style={styles.form}>
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: theme.colors.text }]}>Target Price (₹) *</Text>
            <TextInput
              style={[styles.input, { backgroundColor: theme.colors.surface, color: theme.colors.text, borderColor: theme.colors.border }]}
              value={targetPrice}
              onChangeText={setTargetPrice}
              placeholder="Enter target price"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="decimal-pad"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: theme.colors.text }]}>Alert Condition</Text>
            <View style={styles.conditionButtons}>
              <TouchableOpacity
                style={[
                  styles.conditionButton,
                  { backgroundColor: theme.colors.surface, borderColor: theme.colors.border },
                  condition === 'above' && { backgroundColor: theme.colors.primary }
                ]}
                onPress={() => setCondition('above')}
              >
                <Ionicons 
                  name="trending-up" 
                  size={20} 
                  color={condition === 'above' ? theme.colors.surface : theme.colors.primary}
                />
                <Text style={[
                  styles.conditionText,
                  { color: condition === 'above' ? theme.colors.surface : theme.colors.primary }
                ]}>
                  Above
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.conditionButton,
                  { backgroundColor: theme.colors.surface, borderColor: theme.colors.border },
                  condition === 'below' && { backgroundColor: theme.colors.primary }
                ]}
                onPress={() => setCondition('below')}
              >
                <Ionicons
                  name="trending-down"
                  size={20}
                  color={condition === 'below' ? theme.colors.surface : theme.colors.primary}
                />
                <Text style={[
                  styles.conditionText,
                  { color: condition === 'below' ? theme.colors.surface : theme.colors.primary }
                ]}>
                  Below
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Alert Type</Text>
            <View style={styles.typeSelector}>
              <View style={styles.typeOption}>
                <View style={styles.typeInfo}>
                  <Text style={styles.typeTitle}>One-time Alert</Text>
                  <Text style={styles.typeDescription}>
                    Alert once when condition is met, then disable
                  </Text>
                </View>
                <Switch
                  value={alertType === 'one-time'}
                  onValueChange={(value) => setAlertType(value ? 'one-time' : 'recurring')}
                  trackColor={{ false: '#E5E5EA', true: '#34C759' }}
                  thumbColor="#FFFFFF"
                />
              </View>
              <View style={styles.typeOption}>
                <View style={styles.typeInfo}>
                  <Text style={styles.typeTitle}>Recurring Alert</Text>
                  <Text style={styles.typeDescription}>
                    Alert every time the condition is met
                  </Text>
                </View>
                <Switch
                  value={alertType === 'recurring'}
                  onValueChange={(value) => setAlertType(value ? 'recurring' : 'one-time')}
                  trackColor={{ false: '#E5E5EA', true: '#34C759' }}
                  thumbColor="#FFFFFF"
                />
              </View>
            </View>
          </View>

          {targetPrice && (
            <View style={styles.previewCard}>
              <Text style={styles.previewTitle}>Alert Preview</Text>
              <Text style={styles.previewText}>{getAlertDescription()}</Text>
            </View>
          )}

          {existingAlerts.length > 0 && (
            <View style={styles.existingAlertsCard}>
              <Text style={styles.existingAlertsTitle}>
                Existing Alerts ({existingAlerts.length})
              </Text>
              {existingAlerts.map((alert) => (
                <View key={alert.id} style={styles.existingAlert}>
                  <View style={styles.alertInfo}>
                    <Text style={styles.alertCondition}>
                      {alert.condition === 'above' ? '↗️' : '↘️'} ₹{alert.targetPrice.toFixed(2)}
                    </Text>
                    <Text style={styles.alertStatus}>
                      {alert.triggered ? 'Triggered' : 'Active'} • {alert.type}
                    </Text>
                  </View>
                  <View style={[
                    styles.alertStatusBadge,
                    alert.triggered ? styles.triggeredBadge : styles.activeBadge
                  ]}>
                    <Text style={styles.alertStatusText}>
                      {alert.triggered ? 'Triggered' : 'Active'}
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  cancelButton: {
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  cancelText: {
    fontSize: 16,
    color: '#007AFF',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  saveButton: {
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  disabledButton: {
    opacity: 0.5,
  },
  saveText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#8E8E93',
  },
  stockCard: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  stockTicker: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 4,
  },
  stockName: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
    marginBottom: 16,
  },
  currentPriceSection: {
    alignItems: 'center',
  },
  currentPriceLabel: {
    fontSize: 14,
    color: '#8E8E93',
    marginBottom: 4,
  },
  currentPrice: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 4,
  },
  priceChange: {
    fontSize: 16,
    fontWeight: '500',
  },
  form: {
    padding: 16,
  },
  inputGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 12,
  },
  input: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#000000',
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  conditionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  conditionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#007AFF',
    backgroundColor: '#FFFFFF',
    gap: 8,
  },
  selectedCondition: {
    backgroundColor: '#007AFF',
  },
  conditionText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
  },
  selectedConditionText: {
    color: '#FFFFFF',
  },
  typeSelector: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    gap: 16,
  },
  typeOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  typeInfo: {
    flex: 1,
    marginRight: 12,
  },
  typeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  typeDescription: {
    fontSize: 14,
    color: '#8E8E93',
    lineHeight: 18,
  },
  previewCard: {
    backgroundColor: '#F0F8FF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  previewTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
    marginBottom: 8,
  },
  previewText: {
    fontSize: 14,
    color: '#007AFF',
    lineHeight: 20,
  },
  existingAlertsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
  },
  existingAlertsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 12,
  },
  existingAlert: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
  },
  alertInfo: {
    flex: 1,
  },
  alertCondition: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000000',
    marginBottom: 2,
  },
  alertStatus: {
    fontSize: 12,
    color: '#8E8E93',
  },
  alertStatusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  activeBadge: {
    backgroundColor: '#E8F5E8',
  },
  triggeredBadge: {
    backgroundColor: '#FFF3E0',
  },
  alertStatusText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#34C759',
  },
});
