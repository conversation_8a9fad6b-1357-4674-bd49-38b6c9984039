import { getStockChangeColor, useTheme } from '@/contexts/ThemeContext';
import { useAppStore } from '@/store/appStore';
import { getMarketStatus } from '@/utils/dataProcessing';
import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useMemo } from 'react';
import { Alert, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function StockDetailsScreen() {
  const { ticker } = useLocalSearchParams<{ ticker: string }>();
  const {
    stocks,
    watchlist,
    priceAlerts,
    addToWatchlist,
    removeFromWatchlist
  } = useAppStore();

  const { theme } = useTheme();

  const stock = useMemo(() => {
    return stocks.find(s => s.ticker === ticker);
  }, [stocks, ticker]);

  const isInWatchlist = useMemo(() => {
    return watchlist.some(item => item.ticker === ticker);
  }, [watchlist, ticker]);

  const stockAlerts = useMemo(() => {
    return priceAlerts.filter(alert => alert.ticker === ticker && !alert.triggered);
  }, [priceAlerts, ticker]);

  const marketStatus = getMarketStatus();

  const handleWatchlistToggle = async () => {
    try {
      if (isInWatchlist) {
        await removeFromWatchlist(ticker);
      } else {
        await addToWatchlist(ticker);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to update watchlist');
    }
  };

  const handlePriceAlert = () => {
    router.push(`/price-alert/${ticker}`);
  };

  const handleClose = () => {
    router.back();
  };

  if (!stock) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
            <Ionicons name="close" size={24} color="#000000" />
          </TouchableOpacity>
          <Text style={styles.title}>Stock Details</Text>
          <View style={styles.placeholder} />
        </View>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Stock not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={[styles.header, { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border }]}>
        <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
          <Ionicons name="close" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={[styles.title, { color: theme.colors.text }]}>Stock Details</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={[styles.stockHeader, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.stockInfo}>
            <Text style={[styles.stockTicker, { color: theme.colors.text }]}>{stock.ticker}</Text>
            <Text style={[styles.stockName, { color: theme.colors.textSecondary }]}>{stock.ticker_name}</Text>
            <Text style={[styles.stockSector, { color: theme.colors.textSecondary }]}>{stock.indices}</Text>
          </View>

          <View style={styles.priceSection}>
            <Text style={[styles.currentPrice, { color: theme.colors.text }]}>₹{stock.ltp.toFixed(2)}</Text>
            <Text style={[styles.priceChange, { color: getStockChangeColor(stock.point_change, theme) }]}>
              {stock.formattedChange}
            </Text>
          </View>
        </View>

        <View style={[styles.marketStatusCard, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.marketStatusHeader}>
            <Ionicons
              name={marketStatus.isOpen ? "radio-button-on" : "radio-button-off"}
              size={16}
              color={marketStatus.isOpen ? theme.colors.success : theme.colors.error}
            />
            <Text style={[
              styles.marketStatusText,
              { color: marketStatus.isOpen ? theme.colors.success : theme.colors.error }
            ]}>
              {marketStatus.message}
            </Text>
          </View>
          <Text style={[styles.lastUpdated, { color: theme.colors.textSecondary }]}>
            Last updated: {new Date(stock.calculated_on).toLocaleTimeString()}
          </Text>
        </View>

        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[
              styles.actionButton,
              { backgroundColor: theme.colors.surface, borderColor: theme.colors.border },
              isInWatchlist && { backgroundColor: theme.colors.warning }
            ]}
            onPress={handleWatchlistToggle}
          >
            <Ionicons
              name={isInWatchlist ? "star" : "star-outline"}
              size={20}
              color={isInWatchlist ? theme.colors.surface : theme.colors.primary}
            />
            <Text style={[
              styles.actionButtonText,
              { color: isInWatchlist ? theme.colors.surface : theme.colors.primary }
            ]}>
              {isInWatchlist ? 'In Watchlist' : 'Add to Watchlist'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}
            onPress={handlePriceAlert}
          >
            <Ionicons name="notifications-outline" size={20} color={theme.colors.primary} />
            <Text style={[styles.actionButtonText, { color: theme.colors.primary }]}>Set Price Alert</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.priceDetails}>
          <Text style={styles.sectionTitle}>Price Details</Text>
          <View style={styles.detailsGrid}>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Open</Text>
              <Text style={styles.detailValue}>₹{stock.open.toFixed(2)}</Text>
            </View>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>High</Text>
              <Text style={styles.detailValue}>₹{stock.high.toFixed(2)}</Text>
            </View>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Low</Text>
              <Text style={styles.detailValue}>₹{stock.low.toFixed(2)}</Text>
            </View>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Previous Close</Text>
              <Text style={styles.detailValue}>₹{stock.previousClosing.toFixed(2)}</Text>
            </View>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Volume</Text>
              <Text style={styles.detailValue}>{stock.formattedVolume}</Text>
            </View>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Turnover</Text>
              <Text style={styles.detailValue}>{stock.formattedAmount}</Text>
            </View>
          </View>
        </View>

        {stockAlerts.length > 0 && (
          <View style={styles.alertsSection}>
            <Text style={styles.sectionTitle}>Active Price Alerts ({stockAlerts.length})</Text>
            {stockAlerts.map((alert) => (
              <View key={alert.id} style={styles.alertCard}>
                <View style={styles.alertInfo}>
                  <Text style={styles.alertCondition}>
                    {alert.condition === 'above' ? '↗️ Above' : '↘️ Below'} ₹{alert.targetPrice.toFixed(2)}
                  </Text>
                  <Text style={styles.alertType}>{alert.type} alert</Text>
                </View>
                <View style={styles.alertStatus}>
                  <Text style={styles.alertStatusText}>Active</Text>
                </View>
              </View>
            ))}
          </View>
        )}

        <View style={styles.additionalInfo}>
          <Text style={styles.sectionTitle}>Additional Information</Text>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Data Source</Text>
            <Text style={styles.infoValue}>{stock.datasource}</Text>
          </View>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Last Trade Volume</Text>
            <Text style={styles.infoValue}>{stock.ltv}</Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  closeButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#8E8E93',
  },
  stockHeader: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  stockInfo: {
    marginBottom: 16,
  },
  stockTicker: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 4,
  },
  stockName: {
    fontSize: 16,
    color: '#8E8E93',
    marginBottom: 4,
    lineHeight: 22,
  },
  stockSector: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
  },
  priceSection: {
    alignItems: 'center',
  },
  currentPrice: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 4,
  },
  priceChange: {
    fontSize: 18,
    fontWeight: '600',
  },
  marketStatusCard: {
    backgroundColor: '#FFFFFF',
    margin: 16,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  marketStatusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4,
  },
  marketStatusText: {
    fontSize: 16,
    fontWeight: '600',
  },
  lastUpdated: {
    fontSize: 14,
    color: '#8E8E93',
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    gap: 12,
    marginBottom: 16,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#007AFF',
    backgroundColor: '#FFFFFF',
    gap: 8,
  },
  activeWatchlistButton: {
    backgroundColor: '#FFD700',
    borderColor: '#FFD700',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
  },
  activeWatchlistButtonText: {
    color: '#FFFFFF',
  },
  priceDetails: {
    backgroundColor: '#FFFFFF',
    margin: 16,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 16,
  },
  detailsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  detailItem: {
    width: '45%',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 14,
    color: '#8E8E93',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
  },
  alertsSection: {
    backgroundColor: '#FFFFFF',
    margin: 16,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  alertCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
  },
  alertInfo: {
    flex: 1,
  },
  alertCondition: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000000',
    marginBottom: 2,
  },
  alertType: {
    fontSize: 12,
    color: '#8E8E93',
    textTransform: 'capitalize',
  },
  alertStatus: {
    backgroundColor: '#E8F5E8',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  alertStatusText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#34C759',
  },
  additionalInfo: {
    backgroundColor: '#FFFFFF',
    margin: 16,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
  },
  infoLabel: {
    fontSize: 16,
    color: '#8E8E93',
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000000',
  },
});
