// screens/AllStocksScreen.tsx
import { useTheme } from '@/contexts/ThemeContext';
import { useAppStore } from '@/store/appStore';
import { getTopGainersAndLosers } from '@/utils/dataProcessing';
import React, { useMemo } from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import NepseStockChart from '../../../components/NepseStockChart ';
import SectorPerformance from '../../../components/SectorPerformance';
import TopMovers from '../../../components/TopMovers';

export default function ExploreScreen() {
  const { stocks } = useAppStore();
  const { theme } = useTheme();

  const topGainers = useMemo(() => {
    const { gainers } = getTopGainersAndLosers(stocks, 20);
    return gainers.filter(stock => stock.percentage_change > 0);
  }, [stocks]);

  const topLosers = useMemo(() => {
    const { losers } = getTopGainersAndLosers(stocks, 20);
    return losers.filter(stock => stock.percentage_change < 0);
  }, [stocks]);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.chartContainer}>
          <NepseStockChart />
        </View>

        <View style={styles.sectorContainer}>
          <SectorPerformance />
        </View>

        <View style={styles.topMoversContainer}>
          <TopMovers
            gainers={topGainers}
            losers={topLosers}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  scrollContent: {
    padding: 16,
  },
  chartContainer: {
    marginBottom: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  sectorContainer: {
    marginBottom: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  topMoversContainer: {
    marginBottom: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
});