import { useTheme } from '@/contexts/ThemeContext';
import { useAppStore } from '@/store/appStore';
import { ProcessedStock, getTopGainersAndLosers } from '@/utils/dataProcessing';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useMemo } from 'react';
import { FlatList, RefreshControl, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function LosersScreen() {
  const {
    stocks,
    isRefreshing,
    refreshAllData,
  } = useAppStore();

  const { theme } = useTheme();

  const topLosers = useMemo(() => {
    const { losers } = getTopGainersAndLosers(stocks, 20);
    return losers.filter(stock => stock.percentage_change < 0);
  }, [stocks]);

  const handleStockPress = (stock: ProcessedStock) => {
    router.push(`/stock-details/${stock.ticker}`);
  };

  const renderStockItem = ({ item: stock, index }: { item: ProcessedStock; index: number }) => {
    return (
      <TouchableOpacity style={[styles.stockCard, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]} onPress={() => handleStockPress(stock)}>
        <View style={styles.rankSection}>
          <View style={[styles.rankBadge, { backgroundColor: theme.colors.error + '20' }]}>
            <Text style={[styles.rankText, { color: theme.colors.error }]}>#{index + 1}</Text>
          </View>
        </View>

        <View style={styles.stockInfo}>
          <Text style={[styles.stockTicker, { color: theme.colors.text }]}>{stock.ticker}</Text>
          <Text style={[styles.stockName, { color: theme.colors.textSecondary }]} numberOfLines={2}>
            {stock.ticker_name}
          </Text>
        </View>

        <View style={styles.priceSection}>
          <Text style={[styles.currentPrice, { color: theme.colors.text }]}>{stock.formattedLTP}</Text>
          <View style={styles.changeContainer}>
            <Ionicons name="trending-down" size={16} color={theme.colors.error} />
            <Text style={[styles.priceChange, { color: theme.colors.error }]}>
              {stock.percentage_change.toFixed(2)}%
            </Text>
          </View>
          <Text style={[styles.pointChange, { color: theme.colors.error }]}>
            ₹{stock.point_change.toFixed(2)}
          </Text>
        </View>

        <View style={styles.volumeSection}>
          <Text style={styles.volumeLabel}>Volume</Text>
          <Text style={styles.volumeValue}>{stock.formattedVolume}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.titleSection}>
        <Ionicons name="trending-down" size={24} color={theme.colors.error} />
        <Text style={[styles.title, { color: theme.colors.text }]}>Top Losers</Text>
      </View>
      <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
        Stocks with highest percentage losses today
      </Text>
      <View style={styles.statsContainer}>
        <Text style={[styles.statsText, { color: theme.colors.textSecondary }]}>
          {topLosers.length} declining stocks
        </Text>
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="trending-down-outline" size={64} color="#8E8E93" />
      <Text style={styles.emptyTitle}>No Losers Today</Text>
      <Text style={styles.emptySubtitle}>
        No stocks are showing negative performance at the moment. Check back later or refresh to see updates.
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <FlatList
        data={topLosers}
        renderItem={renderStockItem}
        keyExtractor={(item) => item.ticker}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={refreshAllData} />
        }
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  listContainer: {
    padding: 16,
  },
  header: {
    marginBottom: 20,
  },
  titleSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
  },
  subtitle: {
    fontSize: 16,
    color: '#8E8E93',
    marginBottom: 12,
  },
  statsContainer: {
    alignItems: 'center',
    backgroundColor: '#FFE8E8',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    alignSelf: 'flex-start',
  },
  statsText: {
    fontSize: 14,
    color: '#FF3B30',
    fontWeight: '600',
  },
  stockCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderLeftWidth: 4,
    borderLeftColor: '#FF3B30',
  },
  rankSection: {
    marginRight: 12,
  },
  rankBadge: {
    backgroundColor: '#FF3B30',
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  rankText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  stockInfo: {
    flex: 1,
    marginRight: 12,
  },
  stockTicker: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  stockName: {
    fontSize: 12,
    color: '#8E8E93',
    lineHeight: 16,
  },
  priceSection: {
    alignItems: 'flex-end',
    marginRight: 12,
  },
  currentPrice: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  changeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 2,
  },
  priceChange: {
    fontSize: 14,
    fontWeight: '600',
  },
  pointChange: {
    fontSize: 12,
    color: '#FF3B30',
    fontWeight: '500',
  },
  volumeSection: {
    alignItems: 'flex-end',
    minWidth: 60,
  },
  volumeLabel: {
    fontSize: 10,
    color: '#8E8E93',
    marginBottom: 2,
  },
  volumeValue: {
    fontSize: 12,
    fontWeight: '500',
    color: '#000000',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#000000',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
    paddingHorizontal: 32,
    lineHeight: 22,
  },
});
