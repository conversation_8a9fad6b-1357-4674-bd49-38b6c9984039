import { useTheme } from '@/contexts/ThemeContext';
import { useAppStore } from '@/store/appStore';
import { ProcessedIPO } from '@/utils/dataProcessing';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { RefreshControl, ScrollView, StyleSheet, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function UpcomingIPOsScreen() {
  const {
    upcomingIPOs,
    isRefreshing,
    refreshAllData,
  } = useAppStore();

  const { theme } = useTheme();

  const renderIPOCard = (ipo: ProcessedIPO) => {
    return (
      <View key={ipo.finid} style={[styles.ipoCard, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}>
        <View style={styles.ipoHeader}>
          <View style={styles.ipoTitleSection}>
            <Text style={[styles.ipoCompanyName, { color: theme.colors.text }]}>{ipo.company_name}</Text>
            <Text style={[styles.ipoSector, { color: theme.colors.textSecondary }]}>{ipo.Sector}</Text>
          </View>
          <View style={styles.ipoStatusSection}>
            <View style={[styles.statusBadge, styles.upcomingBadge, { backgroundColor: theme.colors.primary + '20' }]}>
              <Text style={[styles.statusText, { color: theme.colors.primary }]}>Upcoming</Text>
            </View>
          </View>
        </View>

        <View style={styles.ipoDetails}>
          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: theme.colors.textSecondary }]}>Offer Price</Text>
            <Text style={[styles.detailValue, { color: theme.colors.text }]}>₹{ipo.offerPriceNumber}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: theme.colors.textSecondary }]}>Shares Offered</Text>
            <Text style={[styles.detailValue, { color: theme.colors.text }]}>{ipo.shares_offered.toLocaleString()}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: theme.colors.textSecondary }]}>Issue Manager</Text>
            <Text style={[styles.detailValue, { color: theme.colors.text }]}>{ipo.issue_manager}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: theme.colors.textSecondary }]}>Approval Date</Text>
            <Text style={[styles.detailValue, { color: theme.colors.text }]}>{ipo.formattedOpenDate}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: theme.colors.textSecondary }]}>Status</Text>
            <Text style={[styles.detailValue, { color: theme.colors.text }]}>{ipo.Status}</Text>
          </View>
        </View>

        <View style={styles.ipoFooter}>
          <View style={styles.waitingInfo}>
            <Ionicons name="time-outline" size={16} color="#FF9500" />
            <Text style={styles.waitingText}>
              {ipo.open_date ? 'Opening date to be announced' : 'Waiting for opening date'}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={refreshAllData} />
        }
      >
        {upcomingIPOs.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="calendar-outline" size={64} color={theme.colors.textSecondary} />
            <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>No Upcoming IPOs</Text>
            <Text style={[styles.emptySubtitle, { color: theme.colors.textSecondary }]}>
              There are currently no upcoming IPOs announced. We&apos;ll notify you when new IPOs are scheduled.
            </Text>
          </View>
        ) : (
          <View style={styles.iposList}>
            <View style={styles.listHeader}>
              <Text style={[styles.listTitle, { color: theme.colors.text }]}>Upcoming IPOs ({upcomingIPOs.length})</Text>
              <Text style={[styles.listSubtitle, { color: theme.colors.textSecondary }]}>
                IPOs approved but not yet open for application
              </Text>
            </View>
            {upcomingIPOs.map(renderIPOCard)}
          </View>
        )}

        <View style={[styles.infoCard, { backgroundColor: theme.colors.surface }]}>
          <Ionicons name="bulb" size={24} color={theme.colors.warning} />
          <View style={styles.infoContent}>
            <Text style={[styles.infoTitle, { color: theme.colors.text }]}>Stay Prepared</Text>
            <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
              • Research the company&apos;s business model{'\n'}
              • Check the company&apos;s financial performance{'\n'}
              • Understand the risks involved{'\n'}
              • Set up notifications to know when IPO opens{'\n'}
              • Prepare your investment amount in advance
            </Text>
          </View>
        </View>

        <View style={[styles.notificationCard, { backgroundColor: theme.colors.surface, borderColor: theme.colors.primary }]}>
          <View style={styles.notificationHeader}>
            <Ionicons name="notifications" size={24} color={theme.colors.primary} />
            <Text style={[styles.notificationTitle, { color: theme.colors.primary }]}>IPO Notifications</Text>
          </View>
          <Text style={[styles.notificationText, { color: theme.colors.primary }]}>
            You&apos;ll receive notifications twice daily (9 AM & 8 PM) about open IPOs.
            You can change notification times in Settings.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#000000',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
    paddingHorizontal: 32,
    lineHeight: 22,
  },
  iposList: {
    marginBottom: 16,
  },
  listHeader: {
    marginBottom: 16,
  },
  listTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  listSubtitle: {
    fontSize: 14,
    color: '#8E8E93',
  },
  ipoCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  ipoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  ipoTitleSection: {
    flex: 1,
    marginRight: 12,
  },
  ipoCompanyName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
    lineHeight: 24,
  },
  ipoSector: {
    fontSize: 14,
    color: '#8E8E93',
  },
  ipoStatusSection: {
    alignItems: 'flex-end',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  upcomingBadge: {
    backgroundColor:'#FF9500'
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    
  },
  ipoDetails: {
    marginBottom: 16,
    gap: 8,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: 14,
    color: '#8E8E93',
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#000000',
  },
  ipoFooter: {
    borderTopWidth: 1,
    borderTopColor: '#F2F2F7',
    paddingTop: 12,
  },
  waitingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  waitingText: {
    fontSize: 14,
    color: '#FF9500',
    fontStyle: 'italic',
  },
  infoCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoContent: {
    flex: 1,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#8E8E93',
    lineHeight: 20,
  },
  notificationCard: {
    backgroundColor: '#F0F8FF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  notificationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
  },
  notificationText: {
    fontSize: 14,
    color: '#007AFF',
    lineHeight: 20,
  },
});
