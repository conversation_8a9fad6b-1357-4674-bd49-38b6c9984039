import { useTheme } from '@/contexts/ThemeContext';
import { MaterialTopTabNavigationOptions, createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { withLayoutContext } from 'expo-router';
import { StatusBar, View } from 'react-native'; // Import StatusBar
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { Navigator } = createMaterialTopTabNavigator();
export const MaterialTopTabs = withLayoutContext<
  MaterialTopTabNavigationOptions,
  typeof Navigator
>(Navigator);

export default function StocksTabsLayout() {
  const insets = useSafeAreaInsets();
  const { theme } = useTheme();

  // Determine status bar style based on theme
  const statusBarStyle = theme.dark ? 'light-content' : 'dark-content';

  return (
    <View style={{ flex: 1, paddingTop: insets.top }}>
      {/* Add StatusBar component */}
      <StatusBar
        barStyle={statusBarStyle}
        backgroundColor={theme.colors.background}
        translucent={false}
      />

      <MaterialTopTabs
        screenOptions={{
          tabBarActiveTintColor: theme.colors.primary,
          tabBarInactiveTintColor: theme.colors.textSecondary,
          tabBarIndicatorStyle: {
            backgroundColor: theme.colors.primary,
          },
          tabBarStyle: {
            backgroundColor: theme.colors.surface,
          },
          tabBarLabelStyle: {
            fontSize: 12,
            fontWeight: '600',
          },
          tabBarScrollEnabled: true,
        }}>
        <MaterialTopTabs.Screen
          name="explore"
          options={{
            title: 'Explore',
          }}
        />
        <MaterialTopTabs.Screen
          name="stocks-list"
          options={{
            title: 'All Stocks',
          }}
        />
        {/* <MaterialTopTabs.Screen
          name="losers"
          options={{
            title: 'Top Losers',
          }}
        /> */}
        <MaterialTopTabs.Screen
          name="watchlist"
          options={{
            title: 'Watchlist',
          }}
        />
      </MaterialTopTabs>
    </View>
  );
}